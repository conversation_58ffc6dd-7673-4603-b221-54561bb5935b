<?php
/**
 * JService represents an script for connecting to RESTFUL API
 */
class CRAXlsTemplateService extends JService
{
	public static $apiModule = "cra-xls-templates";
	
	
	/**
	 * Get list items
	 */
	public static function getMyTemplates(){
		if(isset(Yii::app()->session['token'])){
			$data = array(
		    	"query" => "creator_id = ".Yii::app()->user->id,
		    );
			$url = 	self::$baseUrl."/".static::$apiModule."?".http_build_query($data);
			$curl = curl_init($url);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			curl_setopt($curl, CURLOPT_HTTPHEADER, array(
						"Accept: application/json",
						"Authorization: Bearer ".Yii::app()->session['token']
						)); 
			// Make the REST call, returning the result
			$response = curl_exec($curl);

			if ($response === FALSE) {
			    throw new CHttpException(500, 'Connection Failure.');
			}
			$http_status = curl_getinfo($curl, CURLINFO_HTTP_CODE);
			// Convert the result from JSON format to a PHP array
			$result = json_decode($response);
			curl_close($curl);
			if($http_status == 200){
				return $result;
			}
			elseif($http_status == 401 && !isset($result->errorCode)){
				throw new CHttpException(403, 'You are not authorized to perform this action.');
			}
			else{
				throw new CHttpException($http_status, $result->errorMessage.'.');
			}
		}
		else{
			throw new CHttpException(403, 'You are not authorized to perform this action.');
		}
	}
	public static function viewFieldExport($fieldNames){
	    if (sizeof($fieldNames) == sizeof(CraService::$list_all_fields))
	        return "All fields";
        $stringFieldName = "";
        foreach ($fieldNames as $fieldName) {
            if ($fieldName != "")
                $stringFieldName .= CraService::$list_all_fields[$fieldName] . ', ';
        }
        if (strlen($stringFieldName) > 2)
            return substr($stringFieldName, 0, strlen($stringFieldName) - 2);
        return "";
    }
}
