<?php if($count>0):?>
<b><?php echo (($page-1)*RangeService::PAGE_SIZE+1);?>-<?php echo min($page*RangeService::PAGE_SIZE,$count)?></b> of <b><?php echo $count?></b>
<?php else:?>
<b>0</b> of <b>0</b>
<?php endif;?>
<div class="btn-group" <?php if($count==0) echo "style='display:none'";?>>
	<button class="btn btn-default btn-paging <?php if($page<=1) echo "disabled";?>" type="button" id="page_<?php echo max(1,$page-1)?>">
		Prev
	</button>
	<button class="btn btn-hidden btn-primary btn-paging" type="button" id="page_<?php echo $page;?>">
		<?php echo $page;?>
	</button>
	<button class="btn btn-default btn-paging <?php if($page>=$total_pages) echo "disabled";?>" type="button" id="page_<?php echo min($total_pages,$page+1)?>">
		Next
	</button>
</div>
