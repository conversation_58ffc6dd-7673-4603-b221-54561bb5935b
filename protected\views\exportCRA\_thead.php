<tr>
	<th class="w-thumb-sm text-center">
		<div class="checkbox no-margin">
			<label class="ui-checks">
				<input id="checkall-items" type="checkbox" value="">
				<i></i>
			</label>
		</div>
	</th>
    <th class="st-sort w-thumb-md">
        <div data-toggle="dropdown" class="dropdown-toogle">ID</div>
        <div class="dropdown-menu keep-dropdown">
            <div class="dropdown-heading">ID
                <a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
            </div>
            <hr>
            <div class="dropdown-content">
                <div class="row">
                    <div class="col-xs-4">Sort</div>
                    <div class="col-xs-4">
                        <a href="id ASC" class="asort fa fa-sort-alpha-asc"></a>
                    </div>
                    <div class="col-xs-4">
                        <a href="id DESC" class="asort fa fa-sort-alpha-desc"></a>
                    </div>
                </div>
            </div>
        </div>
    </th>
	<th class="st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Name</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Name
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="name ASC" class="asort fa fa-sort-alpha-asc"></a>
					</div>
					<div class="col-xs-4">
						<a href="name DESC" class="asort fa fa-sort-alpha-desc"></a>
					</div>
				</div>
				<hr>
				<div>
					<p><input type="text" operator="like" name="name" class="input-search form-control rounded" placeholder="Range name (ex: CRA Template 1, CRA Template 2 ...)"></p>
				</div>
			</div>
		</div>
	</th> <!-- user class .st-sort-descent to sort descent -->
	<th class="st-sort">
		<div data-toggle="dropdown" class="dropdown-toogle">Export Field</div>
		<div class="dropdown-menu keep-dropdown">
			<div class="dropdown-heading">Export Field
				<a href="#" class="fa fa-times-circle close close-sort-and-grid-search"></a>
			</div>
			<hr>
			<div class="dropdown-content">
				<div class="row">
					<div class="col-xs-4">Sort</div>
					<div class="col-xs-4">
						<a href="fieldNames ASC" class="asort fa fa-sort-alpha-asc"></a>
					</div>
					<div class="col-xs-4">
						<a href="fieldNames DESC" class="asort fa fa-sort-alpha-desc"></a>
					</div>
				</div>
			</div>
		</div>
	</th>
</tr>
