// Export template functionality
$('#create-a-new-export-template').on('change',function(){
	var check = $(this).is(":checked");
	if(check){
		$('#form-group-xls-select-template').hide();
		$('#form-group-xls-template-name').show();
		$('#exportedFields input').prop("checked",false);
	}
	else{
		$('#form-group-xls-select-template').show();
		$('#form-group-xls-template-name').hide();
	}
});

$('#xlsTemplate').change(function(){
	var val = $(this).val();
	if(val == ""){
		$('#exportedFields input').prop("checked",false);
	}
	else{
		$('#select-all-exportedFields').prop("checked",false).trigger("change");
		var fields = $('#xlsTemplate option:selected').attr("data").split(',');
		$.each(fields,function(index,field){
			$('#exportedFields input[value="'+field+'"]').prop("checked",true);
		});
	}
});

// Select all fields functionality
$('#select-all-exportedFields').on('change',function(){
	var check = $(this).is(":checked");
	$('#exportedFields input[type=checkbox]').prop("checked",check).trigger("change");
});

$('#exportedFields input[type=checkbox]').on('change',function(){
	var check = $(this).is(":checked");
	if(!check){
		$('#select-all-exportedFields').prop("checked",false);
	}
})

//Export
$('#xls-export').click(function(){
	var data = getData();
	var page = $('#paging button.btn-primary').attr('id').substring(5);
	data.page = page;
	var onlySelectedCras = $('#only-selected-cras').is(":checked");
	data.onlySelectedCras = onlySelectedCras;

	var createANewExportTemplate = $('#create-a-new-export-template').is(":checked");
	data.createANewExportTemplate = createANewExportTemplate;

	var exportedFields = [];
	$('#exportedFields').find('input').each(function(){
		if($(this).is(":checked")){
			exportedFields.push($(this).val());
		}
	});
	data.exportedFields = exportedFields;

	var templateName = $('#xls-template-name').val();
	data.templateName = templateName;

	var xlsTemplate = $('#xlsTemplate').val();
	data.xlsTemplate = xlsTemplate;

	var params = Object.keys(data).map(function(k) {
	    return encodeURIComponent(k) + '=' + encodeURIComponent(data[k]);
	}).join('&');
	var url = baseUrl+'/cra/xls?'+params;
    window.open(url,'_blank');
   	$('#button-export').find('button').dropdown("toggle");
   	return false;
});

//Assign (following notification pattern)
$(document).on('click', '.assign', function(e){
	e.preventDefault(); // Prevent default link behavior
	// Get selected CRA items
	var data = getData();
	var page = $('#paging button.btn-primary').attr('id').substring(5);
	data.page = page;
	var assigneeId = $(this).attr('id');
	data.assigneeId = assigneeId;

	// Debug: Log the data being sent
	console.log('CRA Assign - Selected items:', data.selected_items);
	console.log('CRA Assign - Assignee ID:', assigneeId);
	console.log('CRA Assign - Full data:', data);
	console.log('CRA Assign - Checkall items value:', $('input#checkall-items').val());

	// Check if any items are selected
	if (!data.selected_items || data.selected_items === '') {
		jubiqAlert('warning', 'Please select at least one CRA request to assign.');
		return false;
	}

	$.ajax({
  	 	url: baseUrl+'/cra/assign',
  	 	type: "POST",
  	 	dataType: "json",
  	 	data: data,
  	 	success: function(result){
  	 		if(result.success){
	        	$("#list-items").html(result.items);
	        	$("#filters-list").html(result.filters);
	        	$("#paging").html(result.paging);
	        	showEnableFields();
	        	$('#button-assign').find('button').dropdown("toggle");
	        	jubiqAlert('success','CRA requests assigned successfully!');
	        } else {
	        	jubiqAlert('danger', result.error || 'Failed to assign CRA requests');
	        }
	    },
	   error: function (result) {
	   		console.error('CRA Assign error:', result);
	        jubiqAlert('danger', result.responseText || 'Error occurred while assigning CRA requests');
	      }
	    });
	return false;
});
