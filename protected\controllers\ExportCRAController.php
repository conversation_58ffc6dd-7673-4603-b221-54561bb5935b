<?php

class ExportCRAController extends Controller
{
	public $layout='main';

	/**
	 * @var CActiveRecord the currently loaded data model instance.
	 */
	private $_model;

	/**
	 * @return array action filters
	 */
	public function filters()
	{
		return array(
			'api'
		);
	}

	/**
	 * Specifies the access control rules.
	 * This method is used by the 'accessControl' filter.
	 * @return array access control rules
	 */
	public function accessApiRules()
	{
		return array(
			array('allow',
				'actions'=>array('write','index','delete','read'),
				'users'=>array('@'),
			),
			array('deny',
				'users'=>array('*'),
			),
		);
	}

	public function actionIndex($page=1)
	{
		$selected_items = isset($_POST['selected_items'])?explode(',',$_POST['selected_items']):null;
		$orders = isset($_POST['orders'])?$_POST['orders']:array();
		$queries = isset($_POST['queries'])?$_POST['queries']:array();
        $queries[] = array(
            'field'=> 'creator_id',
            'operator'=>'=',
            'value'=>Yii::app()->user->id
        );
		$query = isset($_POST['query'])?$_POST['query']:"";
		$count = CRAXlsTemplateService::count($queries,$query);
		$total_pages = ceil($count/RangeService::PAGE_SIZE);

		$page = min($page,$total_pages);
		$page = max(1,$page);
		$items = CRAXlsTemplateService::getList($page,$orders,$queries,$query);
		if(!isset($_POST['is_ajax']) || !$_POST['is_ajax']){
			// Set up search data
			$this->search = array(
				'query' => $query
			);
			$this->render('index',array(
				'items'=>$items,
				'total_pages'=>$total_pages,
				'count'=>$count,
				'page'=>$page,
				));
		}
		else{
			echo json_encode(
				array(
					'success'=>true,
					'items'=>$this->renderPartial('_tbody',array(
							'items'=>$items,
							'selected_items'=>$selected_items
						),true,true),
					'paging'=>$this->renderPartial('_paging',array(
							'page'=>$page,
							'total_pages'=>$total_pages,
							'count'=>$count
						),true,true),
				)
			);
		}
	}

	public function updateData(){
		$selected_items = isset($_POST['selected_items'])?explode(',',$_POST['selected_items']):null;
		$orders = isset($_POST['orders'])?$_POST['orders']:array();
		$queries = isset($_POST['queries'])?$_POST['queries']:array();
        $queries[] = array(
            'field'=> 'creator_id',
            'operator'=>'=',
            'value'=>Yii::app()->user->id
        );
		$query = isset($_POST['query'])?$_POST['query']:"";
		$count = CRAXlsTemplateService::count($queries,$query);
		$total_pages = ceil($count/RangeService::PAGE_SIZE);
		$page = isset($_POST['page'])?$_POST['page']:1;
		$page = min($page,$total_pages);
		$page = max(1,$page);
		$items = CRAXlsTemplateService::getList($page,$orders,$queries,$query);
		
		echo json_encode(
			array(
				'success'=>true,
				'items'=>$this->renderPartial('_tbody',array(
						'items'=>$items,
						'selected_items'=>$selected_items
					),true,true),
				'paging'=>$this->renderPartial('_paging',array(
						'page'=>$page,
						'total_pages'=>$total_pages,
						'count'=>$count
					),true,true),
			)
		);
	}

	public function actionDelete(){
		$selected_items = explode(",",$_POST['selected_items']);
		if(CRAXlsTemplateService::batch($selected_items)){
			$this->updateData();
		}
	}
	
	public function actionRead($id){
		$item = CRAXlsTemplateService::get($id);
		if(isset($item) && $item->id>0){
			echo json_encode(array(
				'success'=>true,
				'item'=>$item,
				));
		}	
	}
	
	public function actionWrite(){
		if(isset($_POST['id']) && $_POST['id']>0){
            $id = $_POST['id'];
            $item = CRAXlsTemplateService::get($id);
            $item->name = isset($_POST['name'])?$_POST['name']:"";
            $item->fieldNames = isset($_POST['fieldNames'])?$_POST['fieldNames']:array();
            CRAXlsTemplateService::updateFull($id,$item);
			$item = CRAXlsTemplateService::get($id);
		}
		else{
			$item = CRAXlsTemplateService::create(array(
				"name"=>isset($_POST['name'])?$_POST['name']:"",
				"fieldNames"=>isset($_POST['fieldNames'])?$_POST['fieldNames']:array(),
			));
		}
		if(isset($item->id) && $item->id > 0){
			$this->updateData();
		}
	}
}
