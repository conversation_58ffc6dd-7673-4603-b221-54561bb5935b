<?php
// Ensure sci_staffs variable is available
if (!isset($sci_staffs)) {
    $sci_staffs = array(); // Fallback to empty array
}
?>
<li id="button-export">
	<div class="btn-group">
		<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">Export <i class="caret"></i></button>
		<div class="dropdown-menu keep-dropdown w-xxl">
			<div class="panel bg-white bg-inherit">
				<div class="panel-heading b-light bg-light">
					<strong>Export</strong>
				</div>
                <div class="panel-heading">
                    <a class="btn btn-default btn-primary" href="<?php echo Yii::app()->createUrl('exportCRA')?>" target="_blank">Export template</a>
                </div>
				<div class="panel-body">
					<div action="" class="form-horizontal">
						<div class="form-group">
							<label for="only-selected-cras" class="col-xs-10 control-label">Only export selected CRAs</label>
							<div class="col-xs-2">
								<input type="checkbox" id="only-selected-cras" name="only-selected-cras">
							</div>
						</div>
						<div class="form-group">
							<label for="create-a-new-export-template" class="col-xs-10 control-label">Create a new export template</label>
							<div class="col-xs-2">
								<input type="checkbox" id="create-a-new-export-template" name="create-a-new-export-template">
							</div>
						</div>
						<div class="form-group" id="form-group-xls-select-template">
							<label for="xlsTemplate" class="col-xs-5 control-label">Export template</label>
							<div class="col-xs-7">
								<select class="form-control" id="xlsTemplate" name="xlsTemplate">
									<option value="">Select export template</option>
									<?php if(isset($xlsTemplates) && is_array($xlsTemplates)): ?>
										<?php foreach($xlsTemplates as $xlsTemplate):?>
										<option value="<?php echo $xlsTemplate->id;?>" data="<?php echo implode(",",$xlsTemplate->fieldNames);?>"><?php echo $xlsTemplate->name;?></option>
										<?php endforeach;?>
									<?php endif; ?>
								</select>
							</div>
						</div>
						<div class="form-group" id="form-group-xls-template-name" style="display:none;">
							<label for="xls-template-name" class="col-xs-5 control-label">Export template name</label>
							<div class="col-xs-7">
								<input type="text" class="form-control" id="xls-template-name">
							</div>
						</div>
						<div class="form-group">
							<label for="exportedFields" class="col-xs-5 control-label">Exported fields</label>
							<div class="col-xs-7">
								<div class="form-group">
									<div class="col-xs-1">
										<input type="checkbox" id="select-all-exportedFields" name="select-all-exportedFields">
									</div>
									<label for="select-all-exportedFields" class="col-xs-10">Select all fields</label>
								</div>
								<ul class="nav" id="exportedFields">
									<?php foreach(CraService::$list_export_fields as $code=>$field):?>
									<li>
										<div class="checkbox no-margin">
											<label class="ui-checks">
												<input type="checkbox" value="<?php echo $code;?>">
												<i></i>
												<?php echo $field;?>
											</label>
										</div>
									</li>
									<?php endforeach;?>
								</ul>
							</div>
						</div>
					</div>
				</div>
				<div class="panel-footer text-right">
					<button class="btn btn-default cancel">Cancel</button> 
					<button class="btn btn-primary" id="xls-export">Export</button>
				</div>
			</div>
		</div>
	</div>
</li>
<?php if(Yii::app()->user->groupName == "SCI Manager"): ?>
<li id="button-assign" style="display: none;">
	<div class="btn-group">
		<button type="button" data-toggle="dropdown" class="btn btn-default navbar-btn navbar-left">Assign/Re-assign <i class="caret"></i></button>
		<ul class="dropdown-menu w-xxl">
			<?php if(isset($sci_staffs) && is_array($sci_staffs)): ?>
				<?php foreach($sci_staffs as $sci_staff):?>
				<li class="assign" id="<?php echo $sci_staff->id;?>"><a href=""><?php echo $sci_staff->fullName;?></a></li>
				<?php endforeach;?>
			<?php else: ?>
				<li><a href="">No SCI staff available</a></li>
			<?php endif; ?>
		</ul>
	</div>
</li>
<li id="button-cancel" style="display: none;">
	<button type="button" class="btn btn-default navbar-btn navbar-left">Cancel</button>
</li>
<?php endif; ?>