var baseModule = "exportCRA";

function getCheckboxActions(type){
	switch(type){
		case "SINGULAR":
			return ["button-delete"];
			break;
		case "PLURALITY":
			return ["button-delete"];
			break;
		case "ALL":
			return ["button-delete"];
			break;
		default:
			return [];
	}
};

function getDataPopup(){    
	var data = {};
    data.id = $('#popup-item #id').val();
	data.name  = $('#popup-item #name').val();
	var fieldNames = [];
	$('#popup-item input.checkbox-export-field:checked').each(function(key,field){
		if ($(field).val() != '')
			fieldNames.push($(field).val());
	});
	data.fieldNames = fieldNames;
    return data;
}
  
function resetDataPopup() {
	$('#popup-item #id').val("");
	$('#popup-item #name').val("");
	$('#popup-item input.checkbox-export-field').prop('checked', false);
}

function setDataPopup(item) {
	$('#popup-item #id').val(item.id);
	$('#popup-item #name').val(item.name);
	$('#popup-item input.checkbox-export-field').prop('checked', false);
	if (item.fieldNames && item.fieldNames.length > 0) {
		$.each(item.fieldNames, function(index, fieldName) {
			$('#popup-item input.checkbox-export-field[value="' + fieldName + '"]').prop('checked', true);
		});
	}
}

$(document).ready(function() {
	// Handle check all export fields
	$('#checkall-export-fields').change(function() {
		var isChecked = $(this).is(':checked');
		$('#popup-item input.checkbox-export-field').not(this).prop('checked', isChecked);
	});
	
	// Handle individual checkbox changes
	$('#popup-item').on('change', 'input.checkbox-export-field', function() {
		if (!$(this).is('#checkall-export-fields')) {
			var totalCheckboxes = $('#popup-item input.checkbox-export-field').not('#checkall-export-fields').length;
			var checkedCheckboxes = $('#popup-item input.checkbox-export-field:checked').not('#checkall-export-fields').length;
			$('#checkall-export-fields').prop('checked', totalCheckboxes === checkedCheckboxes);
		}
	});
});
