var baseModule = "exportCRA";

function getCheckboxActions(type){
	switch(type){
		case "SINGULAR":
			return ["button-delete"];
			break;
		case "PLURALITY":
			return ["button-delete"];
			break;
		case "ALL":
			return ["button-delete"];
			break;
		default:
			return [];
	}
};

function getDataPopup(){    
	var data = {};
    data.id = $('#popup-item #id').val();
	data.name  = $('#popup-item #name').val();
	var fieldNames = [];
	$('#popup-item input.checkbox-export-field:checked').each(function(key,field){
		if ($(field).val() != '')
			fieldNames.push($(field).val());
	});
	data.fieldNames = fieldNames;
    return data;
}
  
function resetDataPopup() {
	$('#popup-item #id').val("");
	$('#popup-item #name').val("");
	$('#popup-item input.checkbox-export-field').prop('checked', false);
}

function updateDataPopup(item){
	resetDataPopup();
	$('#popup-item #id').val(item.id);
	$('#popup-item #name').val(item.name);
	$.each(item.fieldNames,function(key,name){
		$('#popup-item :checkbox[value="'+name+'"]').prop('checked',true);
	});
}

//Check all
$('#checkall-export-fields').on('change',function(){
	var check = $(this).is(":checked");
	$('.checkbox-export-field').prop("checked",check);
});

//Check
$(document).on('change','.checkbox-export-field',function(){
	var check = $(this).is(":checked");
	if(check){
		if($('.checkbox-export-field').not(':checked').length == 0){
			$('#checkall-export-fields').prop("checked",true);
		}
	}
	else{
		$('#checkall-export-fields').prop("checked",false);
	}
});
