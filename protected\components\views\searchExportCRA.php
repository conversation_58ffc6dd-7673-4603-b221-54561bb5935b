<form id="search-form" class="navbar-form nav-form-search navbar-form-sm navbar-right" role="search">
    <div class="form-group">
        <div class="input-group">
			<span class="input-group-btn">
				<button type="submit" class="search-query btn btn-sm no-bg btn-icon no-shadow no-padder">
					<i class="ti-search"></i>
				</button>
			</span>
            <input type="text" class="form-control input-queries input-sm no-bg no-border" placeholder="field OPERATOR value of field">
            <div class="dropdown-menu p-m keep-dropdown" id="popup-config-query">
                <table class="">
                    <tbody>
                    <tr class="build-query">
                        <td>
                            <select style="display:none;" name="" id="" class="select-and-or form-control input-sm">
                                <option value="">Select AND/OR</option>
                                <option value="AND">AND</option>
                                <option value="OR">OR</option>
                            </select>
                        </td>
                        <td>
                            <select name="field" class="select-field form-control input-sm">
                                <option value="">Select Field</option>
                                <option value="name" typeData="string">Name</option>
                                <option value="fieldsName" typeData="string">Fields Name</option>
                            </select>
                        </td>
                        <td>
                            <select name="" id="" class="select-operator form-control input-sm">
                                <option value="">Select Operator</option>
                                <option value="equal">Equal</option>
                                <option value="notEqual">Not Equal</option>
                                <option value="greater">Greater</option>
                                <option value="less">Less</option>
                                <option value="greaterEqual">Greater And Equal</option>
                                <option value="lessEqual">Less And Equal</option>
                                <option value="contain">Contain</option>
                                <option value="endWith">End with</option>
                                <option value="startWith">Start with</option>
                            </select>
                        </td>
                        <td>
                            <input class="form-control input-sm input-value" type="text" placeholder="Value of field">
                        </td>
                        <td>
                            <button class="add-query btn btn-primary btn-sm" ><i class="glyphicon glyphicon-plus"></i></button>
                        </td>
                        <td>
                            <button class="btn btn-default btn-sm remove-query"><i class="glyphicon glyphicon-minus"></i></button>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <button class="btn btn-primary" id="search-query">Search</button>
                        </td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td colspan="2" id="clear-search">Clear this search</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</form>
