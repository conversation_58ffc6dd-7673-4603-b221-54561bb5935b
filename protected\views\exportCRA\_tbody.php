<?php $index=0;?>
<?php foreach($items as $item):?>
<tr>
	<td class="text-center">
		<div class="checkbox no-margin">
			<label class="ui-checks">
				<input type="checkbox" value="<?php echo $item->id;?>" class="check-item" <?php if(isset($selected_items) && in_array($item->id,$selected_items)) echo 'checked';?>>
				<i></i>
			</label>
		</div>
	</td>
    <td class="viewPopup"><?php echo $item->id;?></td>
    <td class="viewPopup"><?php echo $item->name;?></td>
	<td class="viewPopup"><?php echo CRAXlsTemplateService::viewFieldExport($item->fieldNames);?>
    </td>
</tr>
<?php $index++;?>
<?php endforeach;?>
