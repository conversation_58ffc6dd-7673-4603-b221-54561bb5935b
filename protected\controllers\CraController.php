<?php
/**
 * CRA Controller for managing CRA Requests
 */
class <PERSON>ra<PERSON>ontroller extends Controller {
    public $layout = 'main';

    /**
     * Access control for different user roles
     */
    public function accessApiRules() {
        return array(
            array('allow',
                'actions' => array('write','create','update','uploadFiles'),
                'apiRoles' => array('API:CRA:CREATE','API:CRA:UPDATE'),
                'users' => array('@'),
            ),
            array('allow',
                'actions' => array('cancel'),
                'apiRoles' => array('API:CRA:DELETE'),
                'users' => array('@'),
            ),
            array('allow',
                'actions' => array('index','read','getExpositionDetails','getRequestData'),
                'apiRoles' => array('API:CRA:FIND'),
                'users' => array('@'),
            ),
            array('allow',
                'actions' => array('updateEnableFields'),
                'apiRoles' => array('API:USERPREFERENCE:UPDATE'),
                'users' => array('@'),
            ),
            array(
            	'allow',
            	'actions' => array('createFilter'),
            	'apiRoles' => array('API:USERPREFERENCE:UPDATE'),
            	'users' => array('@'),
            ),
            array('deny', 'users' => array('*')),
        );
    }

    public function actionIndex($page = 1) {
        // Handle POST data for search and pagination
        if (isset($_POST) && sizeof($_POST) > 0) {
            Yii::app()->session['cra'] = $_POST;
        }
        $data = Yii::app()->session['cra'];

        if ($page == null) {
            if (!isset($data['page']) || $data['page'] == null) {
                $page = 1;
            } else {
                $page = $data['page'];
            }
        }
        $data['page'] = $page;
        Yii::app()->session['cra'] = $data;

        // Extract search parameters
        $selected_items = isset($data['selected_items']) ? explode(',', $data['selected_items']) : null;
        $orders = isset($data['orders']) ? $data['orders'] : array();
        $queries = isset($data['queries']) ? $data['queries'] : array();
        $query = isset($data['query']) ? $data['query'] : "";

        // Get current filter
        $filter = isset($data['filter']) ? $data['filter'] : null;

        // Use CraService for data retrieval
        try {
            $count = CraService::count($queries, $query, $filter);
            $total_pages = ceil($count / CraService::PAGE_SIZE);
            $items = CraService::getList($page, $orders, $queries, $query, $filter);
        } catch (Exception $e) {
            // Fallback to empty data if API is not available
            $count = 0;
            $total_pages = 1;
            $items = array();
        }

        // Set up search data
        $this->search = array(
            'query' => $query
        );

        // Set up CRA-specific filters (use CraService filters)
        $filters = CraService::$filters;

        // Get XLS templates for CRA
        $xlsTemplates = CRAXlsTemplateService::getMyTemplates();

        // Get SCI staffs for assignment functionality (following notification pattern)
        try {
            $sci_staffs = UserService::getListByGroupNames(array('SCI Staff', 'SCI Manager'));
            Yii::log('Successfully loaded ' . count($sci_staffs) . ' SCI staffs', CLogger::LEVEL_INFO);
        } catch (Exception $e) {
            // Fallback to empty array if API is not available
            $sci_staffs = array();
            Yii::log('Failed to load SCI staffs: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            // For debugging, also check if we have a session token
            if (!isset(Yii::app()->session['token'])) {
                Yii::log('No session token available for UserService call', CLogger::LEVEL_ERROR);
            }

            // Temporary fallback for testing - create mock data
            $sci_staffs = array(
                (object) array('id' => 1, 'fullName' => 'Test SCI Manager'),
                (object) array('id' => 2, 'fullName' => 'Test SCI Staff')
            );
            Yii::log('Using fallback SCI staffs data for testing', CLogger::LEVEL_WARNING);
        }

        // Check if this is an AJAX request
        if (isset($_POST['is_ajax']) && $_POST['is_ajax'] == 1) {
            // Return JSON response for AJAX requests
            echo json_encode(array(
                'success' => true,
                'items' => $this->renderPartial('_tbody', array(
                    'items' => $items
                ), true, true),
                'paging' => $this->renderPartial('_paging', array(
                    'page' => $page,
                    'total_pages' => $total_pages,
                    'count' => $count
                ), true, true),
            ));
            return;
        }

        // Render the full view for normal requests
        $this->render('index', array(
            'items' => $items,
            'total_pages' => $total_pages,
            'count' => $count,
            'page' => $page,
            'orders' => $orders,
            'queries' => $queries,
            'filters' => $filters,
            'filter' => $filter,
            'xlsTemplates' => $xlsTemplates,
            'sci_staffs' => $sci_staffs
        ));
    }

    /**
     * Create new CRA request
     */
    public function actionCreate() {
        if (isset($_POST['typeAction'])) {
            try {
                $craForm = new CraForm();
                $craForm->attributes = $_POST;
                $actionType = isset($_POST['typeAction']) ? $_POST['typeAction'] : 'save';

                // Decode JSON messages
                if(isset($_POST['messages'])){
                    $craForm->messages = json_decode($_POST['messages'],true);
                }

                // Validation for submit action (exposition fields required for submission)
                if ($actionType === 'save-submit') {
                    if (empty($craForm->expositionLevel)) {
                        throw new Exception('Exposition Level is required for submission');
                    }
                    if (empty($craForm->expositionDetail)) {
                        throw new Exception('Exposition Detail is required for submission');
                    }
                    if (empty($craForm->advertisementType)) {
                        throw new Exception('Advertisement Type is required for submission');
                    }
                    if (empty($craForm->timeline)) {
                        throw new Exception('Timeline is required for submission');
                    }
                    if (empty($craForm->requestIds)) {
                        throw new Exception('At least one Request ID is required for submission');
                    }
                }

                // For basic save (draft), no exposition validation required
                // Users can save incomplete forms as drafts

                // Always create with WAIT_FOR_MKT_SUBMISSION status first
                // For save-submit, we'll call submit API after creation to transition status
                $craForm->status = 'WAIT_FOR_MKT_SUBMISSION';

                // Process file uploads on form object
                $this->processFileUploadsOnForm($craForm);

                // Validate form (for submit actions, use built-in validation)
                if ($actionType === 'save-submit' && !$craForm->validate()) {
                    $errors = array();
                    foreach ($craForm->getErrors() as $field => $fieldErrors) {
                        $errors[] = $field . ': ' . implode(', ', $fieldErrors);
                    }
                    throw new Exception('Validation failed: ' . implode('; ', $errors));
                }

                // Create the CRA request using form data
                $item = CraService::create($craForm->getData());

                if (isset($item->id) && $item->id > 0) {
                    // Call submit service if action is save-submit (before setting success message)
                    if ($actionType === 'save-submit') {
                        CraService::submit($item->id);
                        $successMessage = 'CRA Request submitted successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: WAIT_FOR_STRAD_DIRECTOR_TO_ASSIGN)';
                    } else {
                        $successMessage = 'CRA Request saved as draft with ID: ' . $item->id;
                        $successMessage .= ' (Status: WAIT_FOR_MKT_SUBMISSION)';
                    }
                    Yii::app()->user->setFlash('success', $successMessage);

                    // Redirect to update page
                    if(in_array(Yii::app()->user->groupName,array("SCI","SCI Manager","SCI Staff"))){
                        $this->redirect(Yii::app()->createUrl('cra/process',array("id"=>$item->id)));
                    }
                    else{
                        $this->redirect(Yii::app()->createUrl('cra/update',array("id"=>$item->id)));
                    }
                } else {
                    throw new Exception('Failed to create CRA Request - Invalid response from API');
                }
            } catch (Exception $e) {
                Yii::app()->user->setFlash('error', 'Failed to create CRA Request: ' . $e->getMessage());
            }
        }

        // Get related data for form
        $notifications = array(); // NotificationService::getList(1, array(), array(), "");

        $this->render('create', array(
            'notifications' => $notifications,
            'expositionLevels' => CraService::$expositionLevels,
            'expositionDetails' => CraService::$expositionDetails,
            'advertisementTypes' => CraService::$advertisementTypes
        ));
    }

    /**
     * AJAX endpoint to get exposition details for a level
     */
    public function actionGetExpositionDetails() {
        $level = $_GET['level'];
        $details = CraService::getExpositionDetailsForLevel($level);

        echo json_encode(array(
            'success' => true,
            'details' => $details
        ));
    }

    /**
     * Read single CRA request
     */
    public function actionRead($id) {
        try {
            $item = CraService::get($id);
            if (isset($item) && $item->id > 0) {
                echo json_encode(array(
                    'success' => true,
                    'item' => $item,
                ));
            } else {
                throw new Exception('CRA Request not found');
            }
        } catch (Exception $e) {
            echo json_encode(array(
                'success' => false,
                'message' => $e->getMessage()
            ));
        }
    }

    /**
     * Write (create/update) CRA request
     */
    public function actionWrite() {
        // Access control - allow both Marketing and SCI groups
        $allowedGroups = array("Marketing", "CPD", "ACD", "PPD", "LUXE", "SCI", "SCI Manager", "SCI Staff");
        if (!in_array(Yii::app()->user->groupName, $allowedGroups)) {
            throw new CHttpException(403, 'Access denied. Only authorized groups can modify CRA requests.');
        }

        if(isset($_POST['typeAction'])){
            $type_action = $_POST['typeAction'];
        }
        else{
            $type_action = "save";
        }

        if(in_array($type_action,array('save','save-submit','save-revise','save-risk-assessment'))){
            try {
                $craForm = new CraForm();

                // Debug logging - log raw POST data
                Yii::log("CRA actionWrite - Raw POST data: " . json_encode($_POST), CLogger::LEVEL_ERROR, 'cra.error');
                error_log("=== CRA RAW POST DATA ===");
                error_log("POST Data: " . json_encode($_POST));
                error_log("=== END POST DATA ===");

                $craForm->attributes = $_POST;

                // Debug logging - log form attributes after assignment
                Yii::log("CRA actionWrite - Form attributes after assignment: " . json_encode($craForm->attributes), CLogger::LEVEL_INFO, 'cra.debug');

                // Decode JSON messages
                if(isset($_POST['messages'])){
                    $craForm->messages = json_decode($_POST['messages'],true);
                }

                // Handle requestIds like advertising module
                if(isset($_POST['requestIds'])){
                    $decodedRequestIds = json_decode($_POST['requestIds']);
                    // Ensure it's an array of integers
                    if (is_array($decodedRequestIds)) {
                        $craForm->requestIds = array_map('intval', $decodedRequestIds);
                    } else {
                        $craForm->requestIds = array();
                    }
                    Yii::log("CRA actionWrite - Processed requestIds: " . json_encode($craForm->requestIds), CLogger::LEVEL_INFO, 'cra.debug');
                }

                // Process file uploads on form object
                $this->processFileUploadsOnForm($craForm);

                // Debug logging - log form data after file processing
                $formData = $craForm->getData();
                Yii::log("CRA actionWrite - Form getData() after file processing: " . json_encode($formData), CLogger::LEVEL_ERROR, 'cra.error');
                error_log("=== CRA FORM DATA ===");
                error_log("Form Data: " . json_encode($formData));
                error_log("=== END FORM DATA ===");

                // Check if form data can be JSON encoded
                $testJson = json_encode($formData);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $jsonError = json_last_error_msg();
                    Yii::log("CRA actionWrite - Form data JSON encoding error: " . $jsonError, CLogger::LEVEL_ERROR, 'cra.debug');
                }

                // Debug specific problematic fields
                if (isset($formData['requestIds'])) {
                    Yii::log("CRA actionWrite - requestIds: " . json_encode($formData['requestIds']) . " (type: " . gettype($formData['requestIds']) . ")", CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['timeline'])) {
                    Yii::log("CRA actionWrite - timeline: " . json_encode($formData['timeline']) . " (type: " . gettype($formData['timeline']) . ")", CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['contentFiles'])) {
                    Yii::log("CRA actionWrite - contentFiles: " . json_encode($formData['contentFiles']) . " (type: " . gettype($formData['contentFiles']) . ")", CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['referencesFiles'])) {
                    Yii::log("CRA actionWrite - referencesFiles: " . json_encode($formData['referencesFiles']) . " (type: " . gettype($formData['referencesFiles']) . ")", CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['expositionLevel'])) {
                    Yii::log("CRA actionWrite - expositionLevel: " . json_encode($formData['expositionLevel']), CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['expositionDetail'])) {
                    Yii::log("CRA actionWrite - expositionDetail: " . json_encode($formData['expositionDetail']), CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['advertisementType'])) {
                    Yii::log("CRA actionWrite - advertisementType: " . json_encode($formData['advertisementType']), CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['proofDocuments'])) {
                    Yii::log("CRA actionWrite - proofDocuments: " . json_encode($formData['proofDocuments']) . " (type: " . gettype($formData['proofDocuments']) . ")", CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['stradRiskAssessmentFiles'])) {
                    Yii::log("CRA actionWrite - stradRiskAssessmentFiles: " . json_encode($formData['stradRiskAssessmentFiles']) . " (type: " . gettype($formData['stradRiskAssessmentFiles']) . ")", CLogger::LEVEL_INFO, 'cra.debug');
                }
                if (isset($formData['messages'])) {
                    Yii::log("CRA actionWrite - messages: " . json_encode($formData['messages']) . " (type: " . gettype($formData['messages']) . ")", CLogger::LEVEL_INFO, 'cra.debug');
                }

                // Process claims data if provided
                try {
                    $claimsProcessingResult = $this->processClaimsData($_POST);
                    if ($claimsProcessingResult === false) {
                        Yii::log('Claims processing failed for CRA ID: ' . (isset($_POST['id']) ? $_POST['id'] : 'unknown'), CLogger::LEVEL_ERROR);
                        // Don't fail the entire save operation if claims processing fails
                        Yii::app()->user->setFlash('warning', 'CRA saved but claims processing encountered issues. Please check claims data.');
                    }
                } catch (Exception $e) {
                    Yii::log('Exception in claims processing: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
                    error_log("CRA Claims Processing Error: " . $e->getMessage());
                    // Don't fail the entire save operation
                    Yii::app()->user->setFlash('warning', 'CRA saved but claims processing failed: ' . $e->getMessage());
                }

                Yii::log("CRA actionWrite: Checking update condition - id=" . (isset($_POST['id']) ? $_POST['id'] : 'NOT_SET') . ", condition=" . (isset($_POST['id']) && $_POST['id'] > 0 ? 'TRUE' : 'FALSE'), CLogger::LEVEL_INFO, 'cra.path');

                if (isset($_POST['id']) && $_POST['id'] > 0) {
                    // Update existing item
                    Yii::log("CRA actionWrite: Taking UPDATE path for ID: " . $_POST['id'], CLogger::LEVEL_INFO, 'cra.path');
                    $id = $_POST['id'];
                    $item = CraService::get($id);

                    // Debug logging - log original item data
                    Yii::log("CRA Update - Original item contentFiles: " . json_encode($item->contentFiles), CLogger::LEVEL_ERROR, 'cra.files');
                    Yii::log("CRA Update - Original item referencesFiles: " . json_encode($item->referencesFiles), CLogger::LEVEL_ERROR, 'cra.files');
                    Yii::log("CRA Update - Original item proofDocuments: " . json_encode($item->proofDocuments), CLogger::LEVEL_ERROR, 'cra.files');

                    // Preserve the current status unless it's being changed by the action
                    $craForm->status = $item->status;

                    // Debug $_FILES and $_POST data for file processing (safe logging)
                    $filesInfo = array();
                    if (isset($_FILES['newContentFiles']['name'])) {
                        $filesInfo['newContentFiles'] = is_array($_FILES['newContentFiles']['name']) ?
                            'ARRAY_COUNT_' . count($_FILES['newContentFiles']['name']) : $_FILES['newContentFiles']['name'];
                    }
                    if (isset($_FILES['newReferencesFiles']['name'])) {
                        $filesInfo['newReferencesFiles'] = is_array($_FILES['newReferencesFiles']['name']) ?
                            'ARRAY_COUNT_' . count($_FILES['newReferencesFiles']['name']) : $_FILES['newReferencesFiles']['name'];
                    }
                    Yii::log("CRA Update - FILES info: " . print_r($filesInfo, true), CLogger::LEVEL_ERROR, 'cra.files');
                    Yii::log("CRA Update - currentContentFiles POST: " . (isset($_POST['currentContentFiles']) ? $_POST['currentContentFiles'] : 'NOT_SET'), CLogger::LEVEL_ERROR, 'cra.files');
                    Yii::log("CRA Update - currentReferencesFiles POST: " . (isset($_POST['currentReferencesFiles']) ? $_POST['currentReferencesFiles'] : 'NOT_SET'), CLogger::LEVEL_ERROR, 'cra.files');

                    // Check if file inputs exist in HTML
                    Yii::log("CRA Update - newContentFiles in _FILES: " . (isset($_FILES['newContentFiles']) ? 'YES' : 'NO'), CLogger::LEVEL_ERROR, 'cra.files');
                    Yii::log("CRA Update - newReferencesFiles in _FILES: " . (isset($_FILES['newReferencesFiles']) ? 'YES' : 'NO'), CLogger::LEVEL_ERROR, 'cra.files');

                    // Process file uploads on form object (like create page)
                    Yii::log("CRA Update - Before file processing: contentFiles=" . json_encode($craForm->contentFiles) . ", referencesFiles=" . json_encode($craForm->referencesFiles), CLogger::LEVEL_ERROR, 'cra.files');
                    $this->processFileUploadsOnForm($craForm);
                    Yii::log("CRA Update - After file processing: contentFiles=" . json_encode($craForm->contentFiles) . ", referencesFiles=" . json_encode($craForm->referencesFiles), CLogger::LEVEL_ERROR, 'cra.files');

                    // Get form data
                    $formData = $craForm->getData();

                    // Debug logging - log form data
                    Yii::log("CRA Update - Form data: " . json_encode($formData), CLogger::LEVEL_INFO, 'cra.update.debug');

                    // Debug specific fields that might cause issues
                    if (isset($formData['requestIds'])) {
                        Yii::log("CRA Update - requestIds type: " . gettype($formData['requestIds']) . ", value: " . json_encode($formData['requestIds']), CLogger::LEVEL_INFO, 'cra.update.debug');
                    }
                    if (isset($formData['timeline'])) {
                        Yii::log("CRA Update - timeline type: " . gettype($formData['timeline']) . ", value: " . json_encode($formData['timeline']), CLogger::LEVEL_INFO, 'cra.update.debug');
                    }
                    if (isset($formData['contentFiles'])) {
                        Yii::log("CRA Update - contentFiles type: " . gettype($formData['contentFiles']) . ", value: " . json_encode($formData['contentFiles']), CLogger::LEVEL_INFO, 'cra.update.debug');
                    }

                    // Update item properties with form data (advertising pattern)
                    foreach($formData as $attribute => $value){
                        $item->$attribute = $value;
                    }

                    // Remove formatted fields that don't exist in backend model
                    // These are added by CraService::processApiResponse() but cause JSON parsing errors
                    unset($item->created_formatted);
                    unset($item->updated_formatted);
                    unset($item->timeline_date);
                    unset($item->timeline_formatted);

                    // Debug logging - log item data before API call
                    $itemJson = json_encode($item);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $jsonError = json_last_error_msg();
                        Yii::log("CRA Update - JSON encoding error for item: " . $jsonError, CLogger::LEVEL_ERROR, 'cra.update.debug');
                        throw new CHttpException(500, 'Item JSON encoding failed: ' . $jsonError);
                    }
                    Yii::log("CRA Update - Item data before API call: " . $itemJson, CLogger::LEVEL_INFO, 'cra.update.debug');
                    Yii::log("CRA Update - All item properties being sent: " . implode(', ', array_keys((array)$item)), CLogger::LEVEL_INFO, 'cra.update.debug');

                    // Call update service (using base JService updateFull like advertising)
                    CraService::updateFull($id, $item);
                } else {
                    // Create new item (using base JService create like advertising)
                    Yii::log("CRA actionWrite: Taking CREATE path", CLogger::LEVEL_INFO, 'cra.path');
                    $item = CraService::create($craForm->getData());
                }

                if($type_action == 'save-submit'){
                    CraService::submit($item->id);
                } elseif($type_action == 'save-risk-assessment'){
                    CraService::saveRiskAssessment($item->id);
                }

                // Verify successful operation before redirecting
                if(isset($item->id) && $item->id > 0){
                    if ($type_action === 'save-submit') {
                        $successMessage = 'CRA Request submitted successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    } elseif ($type_action === 'save-revise') {
                        $successMessage = 'CRA Request revised successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    } elseif ($type_action === 'save-risk-assessment') {
                        $successMessage = 'CRA Risk Assessment saved successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    } else {
                        $successMessage = 'CRA Request saved successfully with ID: ' . $item->id;
                        $successMessage .= ' (Status: ' . (isset($item->status) ? $item->status : 'Unknown') . ')';
                    }
                    Yii::app()->user->setFlash('success', $successMessage);

                    // Redirect based on user role
                    if(in_array(Yii::app()->user->groupName,array("SCI","SCI Manager","SCI Staff"))){
                        $this->redirect(Yii::app()->createUrl('cra/process',array("id"=>$item->id)));
                    }
                    else{
                        $this->redirect(Yii::app()->createUrl('cra/update',array("id"=>$item->id)));
                    }
                } else {
                    throw new Exception('Failed to save CRA Request - Invalid response from API');
                }
            } catch (Exception $e) {
                error_log("CRA actionWrite: Error - " . $e->getMessage());
                Yii::app()->user->setFlash('error', 'Failed to save CRA Request: ' . $e->getMessage());

                // Redirect back to the form with error message based on user role
                if (isset($_POST['id']) && $_POST['id'] > 0) {
                    // Redirect based on user role (same logic as successful save)
                    if(in_array(Yii::app()->user->groupName,array("SCI","SCI Manager","SCI Staff"))){
                        $this->redirect(array('process', 'id' => $_POST['id']));
                    } else {
                        $this->redirect(array('update', 'id' => $_POST['id']));
                    }
                } else {
                    $this->redirect(array('create'));
                }
            }
        }
        elseif($type_action=="cancel"){
            if(isset($_POST['id']) && $_POST['id']>0){
                $id = $_POST['id'];
                $reason = isset($_POST['reason']) ? $_POST['reason'] : '';
                CraService::cancel($id, $reason);
                $this->redirect(Yii::app()->createUrl('cra/index'));
            }
        }
        elseif($type_action=="assign"){
            if(isset($_POST['id']) && $_POST['id']>0){
                $id = $_POST['id'];
                $assigneeId = isset($_POST['assigneeId'])?$_POST['assigneeId']:"";
                CraService::assign($id,$assigneeId);
                $this->redirect(Yii::app()->createUrl('cra/process',array('id'=>$id)));
            }
        }
        elseif($type_action=="approve"){
            if(isset($_POST['id']) && $_POST['id']>0){
                $id = $_POST['id'];
                CraService::approve($id);
                $this->redirect(Yii::app()->createUrl('cra/process',array('id'=>$id)));
            }
        }
    }

    /**
     * Export CRA data to XLS format
     */
    public function actionXls() {
        $fields = isset($_GET['exportedFields']) ? $_GET['exportedFields'] : "";

        if($_GET['createANewExportTemplate'] == "true"){
            $templateName = isset($_GET['templateName'])?$_GET['templateName']:"";
            CRAXlsTemplateService::create(array(
                'name'=>$templateName,
                'fieldNames'=>explode(',',$fields)
            ));
        }
        else{
            $xlsTemplate = isset($_GET['xlsTemplate'])?$_GET['xlsTemplate']:"";
            if($xlsTemplate != ""){
                $template = CRAXlsTemplateService::get($xlsTemplate);
                if($fields != implode(",",$template->fieldNames)){
                    $template->fieldNames = explode(',',$fields);
                    CRAXlsTemplateService::update($template->id,$template);
                }
            }
            else{
                throw new CHttpException(401, 'Select export template.');
            }
        }

        $orders = (isset($_GET['orders']) && $_GET['orders'] != "") ? array($_GET['orders']) : array();
        $queries = (isset($_GET['queries']) && $_GET['queries'] != "") ? array($_GET['queries']) : array();
        $query = isset($_GET['query']) ? $_GET['query'] : "";
        $filter = isset($_GET['filter']) ? $_GET['filter'] : null;

        if ($_GET['onlySelectedCras'] == "true") {
            if ($_GET['selected_items'] != "") {
                $query_selected_items = 'id IN (' . $_GET['selected_items'] . ')';
            } else {
                $query_selected_items = 'false';
            }
            if ($query != "") {
                $query = $query_selected_items . " AND " . $query;
            } else {
                $query = $query_selected_items;
            }
        }

        $file = CraServiceXls::export($fields, $orders, $queries, $query, $filter);
        $file_array = explode("\n\r", $file, 2);
        $header_array = explode("\n", $file_array[0]);
        foreach ($header_array as $header_value) {
            $header_pieces = explode(':', $header_value);
            if (count($header_pieces) == 2) {
                $headers[$header_pieces[0]] = trim($header_pieces[1]);
            }
        }
        header('Content-type: ' . $headers['Content-Type']);
        header('Content-Disposition: ' . $headers['Content-Disposition']);
        echo substr($file_array[1], 1);
    }

    /**
     * Get request data by notification ID
     */
    public function actionGetRequestData($id) {
        try {
            $notification = NotificationService::get($id);
            echo json_encode(
                array(
                    'success' => true,
                    'request' => array(
                        'id' => $notification->id,
                        'brandName' => $notification->brandName,
                        'productName' => $notification->productName,
                        'davNotificationNumber' => isset($notification->davNotificationNumber) ? $notification->davNotificationNumber : "",
                        'davReceivingDate' => isset($notification->davReceivingDate) ? $notification->davReceivingDate : "",
                        'davExpiringDate' => isset($notification->davExpiringDate) ? $notification->davExpiringDate : "",
                    )
                )
            );
        } catch (Exception $e) {
            echo json_encode(
                array(
                    'success' => false,
                    'message' => $e->getMessage()
                )
            );
        }
    }

    /**
     * Update enabled fields for column visibility
     * Follows the same pattern as other modules (notification, advertising, label)
     */
    public function actionUpdateEnableFields()
    {
        $preference = PreferenceService::get(Yii::app()->user->preferenceId);
        if (isset($_POST['fields'])) {
            $fields = $_POST['fields'];

            // Handle migration from old fields to new combined field
            $migratedFields = array();
            foreach ($fields as $field) {
                if ($field === 'expositionLevel' || $field === 'expositionDetail') {
                    // Replace old fields with new combined field
                    if (!in_array('exposition', $migratedFields)) {
                        $migratedFields[] = 'exposition';
                    }
                } else {
                    $migratedFields[] = $field;
                }
            }

            $preference->selectedCraFields = $migratedFields;
            if (PreferenceService::update(Yii::app()->user->preferenceId, $preference)) {
                Yii::app()->user->setState('craFields', $migratedFields);
                echo json_encode(
                    array('success' => true, 'listEnableFields' => $migratedFields)
                );
            }
        }
    }

    /**
     * Process file uploads for CRA requests
     */
    private function processFileUploads(&$data)
    {
        // Process Content Files
        $newContentFiles = array();
        if (isset($_POST['currentContentFiles'])) {
            $currentContentFiles = json_decode($_POST['currentContentFiles'], true);
            $newContentFiles = array_map('trim', $currentContentFiles);
        }

        if (isset($_FILES['newContentFiles'])) {
            foreach ($_FILES['newContentFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newContentFiles']['tmp_name'][$j];
                    $type = $_FILES['newContentFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newContentFiles[$file->id] = $filename;
                }
            }
        }
        $data['contentFiles'] = (object)$newContentFiles;

        // Process References Files
        $newReferencesFiles = array();
        if (isset($_POST['currentReferencesFiles'])) {
            $currentReferencesFiles = json_decode($_POST['currentReferencesFiles'], true);
            $newReferencesFiles = array_map('trim', $currentReferencesFiles);
        }

        if (isset($_FILES['newReferencesFiles']['name'])) {
            foreach ($_FILES['newReferencesFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newReferencesFiles']['tmp_name'][$j];
                    $type = $_FILES['newReferencesFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newReferencesFiles[$file->id] = $filename;
                }
            }
        }
        $data['referencesFiles'] = (object)$newReferencesFiles;

        // Process Proof Documents (if present)
        $newProofDocuments = array();
        if (isset($_POST['currentProofDocuments'])) {
            $newProofDocuments = array_map('trim', json_decode($_POST['currentProofDocuments'], true));
        }

        if (isset($_FILES['newProofDocuments']['name'])) {
            foreach ($_FILES['newProofDocuments']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newProofDocuments']['tmp_name'][$j];
                    $type = $_FILES['newProofDocuments']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newProofDocuments[$file->id] = $filename;
                }
            }
        }
        $data['proofDocuments'] = (object)$newProofDocuments;
    }

    /**
     * Process file uploads for CRA requests using form model pattern
     */
    private function processFileUploadsOnForm(&$craForm)
    {
        // Process Content Files
        $newContentFiles = array();
        if (isset($_POST['currentContentFiles'])) {
            $currentContentFiles = json_decode($_POST['currentContentFiles'], true);
            $newContentFiles = array_map('trim', $currentContentFiles);
        }

        if (isset($_FILES['newContentFiles'])) {
            foreach ($_FILES['newContentFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newContentFiles']['tmp_name'][$j];
                    $type = $_FILES['newContentFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newContentFiles[$file->id] = $filename;
                }
            }
        }
        $craForm->contentFiles = (object)$newContentFiles;

        // Process References Files
        $newReferencesFiles = array();
        if (isset($_POST['currentReferencesFiles'])) {
            $currentReferencesFiles = json_decode($_POST['currentReferencesFiles'], true);
            $newReferencesFiles = array_map('trim', $currentReferencesFiles);
        }

        if (isset($_FILES['newReferencesFiles'])) {
            foreach ($_FILES['newReferencesFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newReferencesFiles']['tmp_name'][$j];
                    $type = $_FILES['newReferencesFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newReferencesFiles[$file->id] = $filename;
                }
            }
        }
        $craForm->referencesFiles = (object)$newReferencesFiles;

        // Process Proof Documents (if present)
        $newProofDocuments = array();
        if (isset($_POST['currentProofDocuments'])) {
            $newProofDocuments = array_map('trim', json_decode($_POST['currentProofDocuments'], true));
        }

        if (isset($_FILES['newProofDocuments'])) {
            foreach ($_FILES['newProofDocuments']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newProofDocuments']['tmp_name'][$j];
                    $type = $_FILES['newProofDocuments']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newProofDocuments[$file->id] = $filename;
                }
            }
        }
        $craForm->proofDocuments = (object)$newProofDocuments;

        // Process STRAD Risk Assessment Files (process page specific)
        $newStradRiskAssessmentFiles = array();
        if (isset($_POST['currentStradRiskAssessmentFiles'])) {
            $currentStradRiskAssessmentFiles = json_decode($_POST['currentStradRiskAssessmentFiles'], true);
            $newStradRiskAssessmentFiles = array_map('trim', $currentStradRiskAssessmentFiles);
        }

        if (isset($_FILES['newStradRiskAssessmentFiles'])) {
            foreach ($_FILES['newStradRiskAssessmentFiles']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['newStradRiskAssessmentFiles']['tmp_name'][$j];
                    $type = $_FILES['newStradRiskAssessmentFiles']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $newStradRiskAssessmentFiles[$file->id] = $filename;
                }
            }
        }
        $craForm->stradRiskAssessmentFiles = (object)$newStradRiskAssessmentFiles;
    }

    /**
     * Update CRA request - accessible only to Marketing groups
     */
    public function actionUpdate($id){
        // Access control - Marketing groups only
        if (!in_array(Yii::app()->user->groupName, array("Marketing", "CPD", "ACD", "PPD", "LUXE"))) {
            throw new CHttpException(403, 'Access denied. Marketing groups only.');
        }

        $item = CraService::get($id);

        // Debug logging to understand the issue
        Yii::log("CRA Item data: " . print_r($item, true), CLogger::LEVEL_INFO, 'cra.debug');

        $requests = array();

        // Safe handling of requestIds - check if property exists and is not null
        if (isset($item->requestIds) && !empty($item->requestIds)) {
            // Handle both string (JSON) and array formats
            $requestIds = $item->requestIds;
            if (is_string($requestIds)) {
                $requestIds = json_decode($requestIds, true);
            }

            if (is_array($requestIds)) {
                foreach($requestIds as $requestId){
                    if (!empty($requestId)) {
                        $notification = NotificationService::get($requestId);
                        if ($notification) {
                            $requests[] = array(
                                'id'=>$notification->id,
                                'brandName'=>$notification->brandName,
                                'productName'=>$notification->productName,
                                'davNotificationNumber'=>isset($notification->davNotificationNumber)?$notification->davNotificationNumber:"",
                                'davReceivingDate'=>isset($notification->davReceivingDate)?$notification->davReceivingDate:"",
                                'davExpiringDate'=>isset($notification->davExpiringDate)?$notification->davExpiringDate:"",
                            );
                        }
                    }
                }
            }
        }
        $histories = HistoryService::getHistoriesOfCra($id);

        // Get claims data for this CRA request
        $claims = $this->getClaimsForCraRequest($id);

        // API connectivity testing disabled since functionality is working
        // Uncomment below to re-enable API testing if needed for debugging
        // $this->testBasicApiConnectivity();

        // Get reference data for dropdowns with role-based access handling
        Yii::log('=== STARTING DATA RETRIEVAL FOR UPDATE PAGE ===', CLogger::LEVEL_INFO);
        Yii::log('User: ' . Yii::app()->user->groupName . ', Token available: ' . (isset(Yii::app()->session['token']) ? 'Yes' : 'No'), CLogger::LEVEL_INFO);

        $robustnessOptions = $this->getRobustnessOptionsForRole();
        $finePenaltyOptions = $this->getFinePenaltyOptionsForRole();

        // Log final results with details
        Yii::log('FINAL RESULTS - Robustness: ' . (is_array($robustnessOptions) ? count($robustnessOptions) : 'null') . ', Fine/Penalty: ' . (is_array($finePenaltyOptions) ? count($finePenaltyOptions) : 'null'), CLogger::LEVEL_INFO);

        if (is_array($robustnessOptions) && count($robustnessOptions) > 0) {
            Yii::log('Sample robustness item: ' . json_encode($robustnessOptions[0]), CLogger::LEVEL_INFO);
        }
        if (is_array($finePenaltyOptions) && count($finePenaltyOptions) > 0) {
            Yii::log('Sample fine/penalty item: ' . json_encode($finePenaltyOptions[0]), CLogger::LEVEL_INFO);
        }

        Yii::log('=== DATA RETRIEVAL COMPLETE ===', CLogger::LEVEL_INFO);

        $this->render('update',array(
            'item'=>$item,
            'histories'=>$histories,
            'requests'=>$requests,
            'claims'=>$claims,
            'robustnessOptions'=>$robustnessOptions,
            'finePenaltyOptions'=>$finePenaltyOptions
        ));
    }

    /**
     * API Proxy for CRA Claims - Marketing Acceptance
     */
    public function actionApiMktAccept()
    {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'PUT' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        // Get claim ID from URL path
        $pathInfo = Yii::app()->request->pathInfo;
        $pathParts = explode('/', $pathInfo);
        $claimId = null;

        // Find claim ID in path: /cra/apiMktAccept/{id}
        for ($i = 0; $i < count($pathParts); $i++) {
            if ($pathParts[$i] === 'apiMktAccept' && isset($pathParts[$i + 1])) {
                $claimId = $pathParts[$i + 1];
                break;
            }
        }

        if (!$claimId) {
            http_response_code(400);
            echo json_encode(['error' => 'Claim ID is required']);
            return;
        }

        // Get POST data
        $mktAcceptedStatus = isset($_POST['mktAcceptedStatus']) ? $_POST['mktAcceptedStatus'] : null;

        if (!$mktAcceptedStatus) {
            http_response_code(400);
            echo json_encode(['error' => 'mktAcceptedStatus is required']);
            return;
        }

        try {
            // Call backend API
            $url = JService::$baseUrl . "/cra-claims/" . $claimId . "/mkt-accept";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query(['mktAcceptedStatus' => $mktAcceptedStatus]));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/x-www-form-urlencoded",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            http_response_code($httpCode);

            if ($httpCode === 200) {
                echo json_encode(['success' => true, 'message' => 'Marketing acceptance updated successfully']);
            } else {
                echo json_encode(['error' => 'Backend API error', 'response' => $response]);
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
    }

    /**
     * API Proxy for CRA Claims - Approval
     */
    public function actionApiApprove()
    {
        header('Content-Type: application/json');

        if ($_SERVER['REQUEST_METHOD'] !== 'PUT' && $_SERVER['REQUEST_METHOD'] !== 'POST') {
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            return;
        }

        // Get claim ID from URL path
        $pathInfo = Yii::app()->request->pathInfo;
        $pathParts = explode('/', $pathInfo);
        $claimId = null;

        // Find claim ID in path: /cra/apiApprove/{id}
        for ($i = 0; $i < count($pathParts); $i++) {
            if ($pathParts[$i] === 'apiApprove' && isset($pathParts[$i + 1])) {
                $claimId = $pathParts[$i + 1];
                break;
            }
        }

        if (!$claimId) {
            http_response_code(400);
            echo json_encode(['error' => 'Claim ID is required']);
            return;
        }

        // Get POST data
        $approvalStatus = isset($_POST['approvalStatus']) ? $_POST['approvalStatus'] : null;
        $approverType = isset($_POST['approverType']) ? $_POST['approverType'] : null;

        if (!$approvalStatus || !$approverType) {
            http_response_code(400);
            echo json_encode(['error' => 'approvalStatus and approverType are required']);
            return;
        }

        try {
            // Call backend API
            $url = JService::$baseUrl . "/cra-claims/" . $claimId . "/approve";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query([
                'approvalStatus' => $approvalStatus,
                'approverType' => $approverType
            ]));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/x-www-form-urlencoded",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            http_response_code($httpCode);

            if ($httpCode === 200) {
                echo json_encode(['success' => true, 'message' => 'Approval status updated successfully']);
            } else {
                echo json_encode(['error' => 'Backend API error', 'response' => $response]);
            }

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
        }
    }

    /**
     * Process CRA request - accessible only to SCI groups
     */
    public function actionProcess($id){
        // Access control - SCI groups only
        if (!in_array(Yii::app()->user->groupName, array("SCI Manager", "SCI Staff"))) {
            throw new CHttpException(403, 'Access denied. SCI groups only.');
        }

        $item = CraService::get($id);
        $requests = array();

        // Safe handling of requestIds - check if property exists and is not null
        if (isset($item->requestIds) && !empty($item->requestIds)) {
            // Handle both string (JSON) and array formats
            $requestIds = $item->requestIds;
            if (is_string($requestIds)) {
                $requestIds = json_decode($requestIds, true);
            }

            if (is_array($requestIds)) {
                foreach($requestIds as $requestId){
                    if (!empty($requestId)) {
                        $notification = NotificationService::get($requestId);
                        if ($notification) {
                            $requests[] = array(
                                'id'=>$notification->id,
                                'brandName'=>$notification->brandName,
                                'productName'=>$notification->productName,
                                'davNotificationNumber'=>isset($notification->davNotificationNumber)?$notification->davNotificationNumber:"",
                                'davReceivingDate'=>isset($notification->davReceivingDate)?$notification->davReceivingDate:"",
                                'davExpiringDate'=>isset($notification->davExpiringDate)?$notification->davExpiringDate:"",
                            );
                        }
                    }
                }
            }
        }
        $histories = HistoryService::getHistoriesOfCra($id);

        // Get claims data for this CRA request
        $claims = $this->getClaimsForCraRequest($id);

        // Get reference data for dropdowns with role-based access handling
        $robustnessOptions = $this->getRobustnessOptionsForRole();
        $finePenaltyOptions = $this->getFinePenaltyOptionsForRole();

        // Get SCI staffs for assignment functionality
        try {
            $sci_staffs = UserService::getListByGroupNames(array('SCI Staff'));
        } catch (Exception $e) {
            $sci_staffs = array();
            Yii::log('Failed to load SCI staffs for process page: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
        }

        $this->render('process',array(
            'item'=>$item,
            'histories'=>$histories,
            'requests'=>$requests,
            'claims'=>$claims,
            'robustnessOptions'=>$robustnessOptions,
            'finePenaltyOptions'=>$finePenaltyOptions,
            'sci_staffs' => $sci_staffs
        ));
    }

    /**
     * Handle file uploads for CRA chat functionality
     */
    public function actionUploadFiles() {
        $resultUpdateFile = array();
        if (isset($_FILES['files']['name'])) {
            foreach ($_FILES['files']['name'] as $j => $filename) {
                if ($filename != "") {
                    $tmpfile = $_FILES['files']['tmp_name'][$j];
                    $type = $_FILES['files']['type'][$j];
                    $curlFile = new CurlFile($tmpfile, $type, $filename);
                    $file = FileService::upload($curlFile);
                    $resultUpdateFile[$file->id] = $filename;
                }
            }
        }
        echo json_encode(
            array(
                'success' => true,
                'fileInfos' => $resultUpdateFile,
            )
        );
    }

    /**
     * Assign CRA requests to users (following notification pattern)
     */
    public function actionAssign() {
        try {
            $selected_items = explode(",", $_POST['selected_items']);
            $assigneeId = isset($_POST['assigneeId']) ? $_POST['assigneeId'] : "";

            if (empty($assigneeId)) {
                throw new Exception('Assignee ID is required');
            }

            if (empty($selected_items) || (count($selected_items) == 1 && empty($selected_items[0]))) {
                throw new Exception('No CRA requests selected');
            }

            $successCount = 0;
            $errors = array();

            foreach ($selected_items as $item_id) {
                if (!empty($item_id)) {
                    try {
                        CraService::assign($item_id, $assigneeId);
                        $successCount++;
                        Yii::log("Successfully assigned CRA {$item_id} to user {$assigneeId}", CLogger::LEVEL_INFO);
                    } catch (Exception $e) {
                        $errors[] = "Failed to assign CRA {$item_id}: " . $e->getMessage();
                        Yii::log("Failed to assign CRA {$item_id}: " . $e->getMessage(), CLogger::LEVEL_ERROR);
                    }
                }
            }

            if ($successCount > 0) {
                // Handle single assignment for process page
                if (isset($_POST['single_assign']) && $_POST['single_assign'] == 'true') {
                    echo json_encode(array('success' => true));
                    return;
                }
                $this->updateData();
            } else {
                echo json_encode(array(
                    'success' => false,
                    'error' => 'Failed to assign any CRA requests. Errors: ' . implode('; ', $errors)
                ));
            }
        } catch (Exception $e) {
            echo json_encode(array(
                'success' => false,
                'error' => $e->getMessage()
            ));
        }
    }

    /**
     * Update data and return JSON response for AJAX calls (following notification pattern)
     */
    private function updateData() {
        $data = Yii::app()->session['cra'];
        $page = isset($data['page']) ? $data['page'] : 1;
        $orders = isset($data['orders']) ? $data['orders'] : array();
        $queries = isset($data['queries']) ? $data['queries'] : array();
        $query = isset($data['query']) ? $data['query'] : "";
        $filter = isset($data['filter']) ? $data['filter'] : null;

        try {
            $count = CraService::count($queries, $query, $filter);
            $total_pages = ceil($count / CraService::PAGE_SIZE);
            $items = CraService::getList($page, $orders, $queries, $query, $filter);
            $filters = CraService::$filters;

            // Get SCI staffs for assignment functionality (needed for AJAX responses)
            try {
                $sci_staffs = UserService::getListByGroupNames(array('SCI Manager', 'SCI Staff'));
            } catch (Exception $e) {
                $sci_staffs = array();
                Yii::log('Failed to load SCI staffs in updateData: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            }
            $xlsTemplates = array();

            echo json_encode(array(
                'success' => true,
                'items' => $this->renderPartial('_tbody', array(
                    'items' => $items
                ), true, true),
                'filters' => $this->renderPartial('_cra_filters', array(
                    'filters' => $filters,
                    'filter' => $filter
                ), true, true),
                'paging' => $this->renderPartial('_paging', array(
                    'page' => $page,
                    'total_pages' => $total_pages,
                    'count' => $count
                ), true, true),
            ));
        } catch (Exception $e) {
            echo json_encode(array(
                'success' => false,
                'error' => $e->getMessage()
            ));
        }
    }

    /**
     * Helper method to fetch claims data for a CRA request
     */
    private function getClaimsForCraRequest($craId) {
        try {
            $url = JService::$baseUrl . "/cra-claims/by-request/" . $craId;
            Yii::log('Fetching claims from URL: ' . $url, CLogger::LEVEL_INFO);

            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            Yii::log('Claims API response - HTTP Code: ' . $httpCode . ', Response: ' . $response, CLogger::LEVEL_INFO);

            if ($httpCode === 200) {
                $claims = json_decode($response);
                $claimsArray = is_array($claims) ? $claims : array();

                Yii::log('Raw claims data before enrichment for CRA ' . $craId . ': ' . json_encode($claimsArray), CLogger::LEVEL_INFO);

                // Enrich claims with robustness and fine/penalty names
                $enrichedClaims = $this->enrichClaimsWithNames($claimsArray);

                Yii::log('Enriched claims data for CRA ' . $craId . ': ' . json_encode($enrichedClaims), CLogger::LEVEL_INFO);
                Yii::log('Claims data for CRA ' . $craId . ': ' . count($enrichedClaims) . ' claims found and enriched', CLogger::LEVEL_INFO);
                return $enrichedClaims;
            } else {
                Yii::log('Claims API failed with HTTP code: ' . $httpCode . ' for CRA ID: ' . $craId, CLogger::LEVEL_WARNING);
            }

            return array();
        } catch (Exception $e) {
            Yii::log('Error fetching claims for CRA ' . $craId . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return array();
        }
    }

    /**
     * Enrich claims data with robustness and fine/penalty names using unified data access
     */
    private function enrichClaimsWithNames($claims) {
        if (empty($claims)) {
            return $claims;
        }

        try {
            Yii::log('Starting claims enrichment for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);

            // Use the same unified data access methods as the dropdowns
            // This ensures Marketing users get the same enriched data as SCI users
            $robustnessOptions = $this->getRobustnessOptionsForRole();
            $finePenaltyOptions = $this->getFinePenaltyOptionsForRole();

            Yii::log('Robustness options count: ' . (is_array($robustnessOptions) ? count($robustnessOptions) : 'null'), CLogger::LEVEL_INFO);
            Yii::log('Fine/penalty options count: ' . (is_array($finePenaltyOptions) ? count($finePenaltyOptions) : 'null'), CLogger::LEVEL_INFO);

            // Create lookup arrays for faster access
            $robustnessLookup = array();
            if (!empty($robustnessOptions)) {
                foreach ($robustnessOptions as $option) {
                    $robustnessLookup[$option->id] = $option->itemName;
                }
            }

            $finePenaltyLookup = array();
            if (!empty($finePenaltyOptions)) {
                foreach ($finePenaltyOptions as $option) {
                    $finePenaltyLookup[$option->id] = $option->itemName;
                }
            }

            // Enrich each claim with names
            foreach ($claims as $claim) {
                // Add robustness name if robustnessId exists
                if (isset($claim->robustnessId) && $claim->robustnessId && isset($robustnessLookup[$claim->robustnessId])) {
                    $claim->robustnessName = $robustnessLookup[$claim->robustnessId];
                }

                // Add fine and penalty name if fineAndPenaltyId exists
                if (isset($claim->fineAndPenaltyId) && $claim->fineAndPenaltyId && isset($finePenaltyLookup[$claim->fineAndPenaltyId])) {
                    $claim->fineAndPenaltyName = $finePenaltyLookup[$claim->fineAndPenaltyId];
                }
            }

            Yii::log('Enriched ' . count($claims) . ' claims with robustness and fine/penalty names for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
            return $claims;

        } catch (Exception $e) {
            Yii::log('Error enriching claims with names: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return $claims; // Return original claims if enrichment fails
        }
    }

    /**
     * Get robustness options with unified access for all authorized roles
     */
    private function getRobustnessOptionsForRole() {
        try {
            // Both SCI and Marketing roles need access to robustness data for claims display
            // Use a unified approach that works for both roles
            return $this->getUnifiedRobustnessData();

        } catch (Exception $e) {
            Yii::log('Error getting robustness options for role ' . Yii::app()->user->groupName . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return array();
        }
    }

    /**
     * Get fine and penalty options with unified access for all authorized roles
     */
    private function getFinePenaltyOptionsForRole() {
        try {
            // Both SCI and Marketing roles need access to fine/penalty data for claims display
            // Use a unified approach that works for both roles
            return $this->getUnifiedFinePenaltyData();

        } catch (Exception $e) {
            Yii::log('Error getting fine/penalty options for role ' . Yii::app()->user->groupName . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return array();
        }
    }

    /**
     * Get unified robustness data that works for both SCI and Marketing roles
     */
    private function getUnifiedRobustnessData() {
        Yii::log('Getting unified robustness data for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);

        // Try the standard API for both SCI and Marketing roles
        // Marketing users need the same data as SCI users for proper claims display
        try {
            Yii::log('Attempting standard API for robustness data', CLogger::LEVEL_INFO);
            $data = RobustnessManagementService::getAll();
            if ($data && !empty($data)) {
                Yii::log('SUCCESS: Robustness data loaded via standard API for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($data), CLogger::LEVEL_INFO);
                return $data;
            }
            Yii::log('WARNING: Standard API returned empty data for robustness', CLogger::LEVEL_WARNING);
        } catch (CHttpException $e) {
            // Handle HTTP exceptions (401, 403, etc.)
            Yii::log('HTTP Exception for robustness API - Code: ' . $e->statusCode . ', Message: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            if ($e->statusCode == 403 || $e->statusCode == 401) {
                Yii::log('Access denied for robustness API, falling back to alternative data source', CLogger::LEVEL_INFO);
            }
        } catch (Exception $e) {
            Yii::log('General Exception for robustness API: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
        }

        // If standard API fails, use alternative data source as fallback
        Yii::log('Using alternative robustness data source for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
        return $this->getAlternativeRobustnessData();
    }

    /**
     * Get unified fine and penalty data that works for both SCI and Marketing roles
     */
    private function getUnifiedFinePenaltyData() {
        Yii::log('Getting unified fine/penalty data for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);

        // Try the standard API for both SCI and Marketing roles
        // Marketing users need the same data as SCI users for proper claims display
        try {
            Yii::log('Attempting standard API for fine/penalty data', CLogger::LEVEL_INFO);
            $data = FineAndPenaltyManagementService::getAll();
            if ($data && !empty($data)) {
                Yii::log('SUCCESS: Fine/penalty data loaded via standard API for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($data), CLogger::LEVEL_INFO);
                return $data;
            }
            Yii::log('WARNING: Standard API returned empty data for fine/penalty', CLogger::LEVEL_WARNING);
        } catch (CHttpException $e) {
            // Handle HTTP exceptions (401, 403, etc.)
            Yii::log('HTTP Exception for fine/penalty API - Code: ' . $e->statusCode . ', Message: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            if ($e->statusCode == 403 || $e->statusCode == 401) {
                Yii::log('Access denied for fine/penalty API, falling back to alternative data source', CLogger::LEVEL_INFO);
            }
        } catch (Exception $e) {
            Yii::log('General Exception for fine/penalty API: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
        }

        // If standard API fails, use alternative data source as fallback
        Yii::log('Using alternative fine/penalty data source for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
        return $this->getAlternativeFinePenaltyData();
    }

    /**
     * Get robustness data through read-only access method for Marketing users
     */
    private function getAlternativeRobustnessData() {
        try {
            Yii::log('Attempting to get robustness data for Marketing role via read-only access', CLogger::LEVEL_INFO);

            // Method 1: Try direct API call to read-only endpoint
            Yii::log('Trying Method 1: Direct API call to read-only endpoint', CLogger::LEVEL_INFO);
            $robustnessData = $this->getReadOnlyRobustnessData();
            if ($robustnessData && !empty($robustnessData)) {
                Yii::log('SUCCESS: Robustness data loaded via read-only API for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($robustnessData), CLogger::LEVEL_INFO);
                return $robustnessData;
            }
            Yii::log('Method 1 failed: No data from read-only API', CLogger::LEVEL_WARNING);

            // Method 2: Try using CRA service with enhanced permissions
            Yii::log('Trying Method 2: CRA service with enhanced permissions', CLogger::LEVEL_INFO);
            $robustnessData = $this->getCraRobustnessDataForDisplay();
            if ($robustnessData && !empty($robustnessData)) {
                Yii::log('SUCCESS: Robustness data loaded via CRA service for role: ' . Yii::app()->user->groupName . ' - Count: ' . count($robustnessData), CLogger::LEVEL_INFO);
                return $robustnessData;
            }
            Yii::log('Method 2 failed: No data from CRA service', CLogger::LEVEL_WARNING);

            // Method 3: Extract from existing claims as fallback
            Yii::log('Trying Method 3: Extract from existing claims as fallback', CLogger::LEVEL_INFO);
            $fallbackData = $this->extractRobustnessDataFromClaims();
            Yii::log('Method 3 result: ' . (is_array($fallbackData) ? count($fallbackData) : 'null') . ' items from claims extraction', CLogger::LEVEL_INFO);

            // Method 4: Hardcoded fallback for testing (temporary)
            if (empty($fallbackData)) {
                Yii::log('Trying Method 4: Hardcoded fallback for testing', CLogger::LEVEL_INFO);
                $fallbackData = $this->getHardcodedRobustnessData();
                Yii::log('Method 4 result: ' . count($fallbackData) . ' hardcoded items', CLogger::LEVEL_INFO);
            }

            return $fallbackData;

        } catch (Exception $e) {
            Yii::log('Alternative robustness data method failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return $this->extractRobustnessDataFromClaims();
        }
    }

    /**
     * Get fine and penalty data through read-only access method for Marketing users
     */
    private function getAlternativeFinePenaltyData() {
        try {
            Yii::log('Attempting to get fine/penalty data for Marketing role via read-only access', CLogger::LEVEL_INFO);

            // Method 1: Try direct API call to read-only endpoint
            $finePenaltyData = $this->getReadOnlyFinePenaltyData();
            if ($finePenaltyData && !empty($finePenaltyData)) {
                Yii::log('Fine/penalty data loaded via read-only API for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
                return $finePenaltyData;
            }

            // Method 2: Try using CRA service with enhanced permissions
            $finePenaltyData = $this->getCraFinePenaltyDataForDisplay();
            if ($finePenaltyData && !empty($finePenaltyData)) {
                Yii::log('Fine/penalty data loaded via CRA service for role: ' . Yii::app()->user->groupName, CLogger::LEVEL_INFO);
                return $finePenaltyData;
            }

            // Method 3: Extract from existing claims as fallback
            $fallbackData = $this->extractFinePenaltyDataFromClaims();

            // Method 4: Hardcoded fallback for testing (temporary)
            if (empty($fallbackData)) {
                Yii::log('Trying Method 4: Hardcoded fine/penalty fallback for testing', CLogger::LEVEL_INFO);
                $fallbackData = $this->getHardcodedFinePenaltyData();
                Yii::log('Method 4 result: ' . count($fallbackData) . ' hardcoded fine/penalty items', CLogger::LEVEL_INFO);
            }

            return $fallbackData;

        } catch (Exception $e) {
            Yii::log('Alternative fine/penalty data method failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return $this->extractFinePenaltyDataFromClaims();
        }
    }

    /**
     * Get robustness data via direct API access for Marketing users
     */
    private function getReadOnlyRobustnessData() {
        try {
            // Use the standard API endpoint with direct curl call
            $url = JService::$baseUrl . "/robustness";
            Yii::log('Attempting direct robustness API call to: ' . $url, CLogger::LEVEL_INFO);

            // Make direct API call bypassing service layer restrictions
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));
            curl_setopt($curl, CURLOPT_TIMEOUT, 15);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            if ($curlError) {
                Yii::log("CURL error for robustness: $curlError", CLogger::LEVEL_ERROR);
                return null;
            }

            Yii::log("Direct robustness API - HTTP Code: $httpCode, Response length: " . strlen($response), CLogger::LEVEL_INFO);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // Convert to objects for consistency
                    $objects = array();
                    foreach ($data as $item) {
                        $objects[] = (object) $item;
                    }
                    Yii::log('Successfully retrieved ' . count($objects) . ' robustness items via direct API', CLogger::LEVEL_INFO);
                    return $objects;
                }
            } else {
                Yii::log("Direct robustness API failed with HTTP $httpCode: " . substr($response, 0, 200), CLogger::LEVEL_WARNING);
            }

            return null;
        } catch (Exception $e) {
            Yii::log('Direct robustness API failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Get fine and penalty data via direct API access for Marketing users
     */
    private function getReadOnlyFinePenaltyData() {
        try {
            // Use the standard API endpoint with direct curl call
            $url = JService::$baseUrl . "/fine-and-penalties";
            Yii::log('Attempting direct fine/penalty API call to: ' . $url, CLogger::LEVEL_INFO);

            // Make direct API call bypassing service layer restrictions
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));
            curl_setopt($curl, CURLOPT_TIMEOUT, 15);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            if ($curlError) {
                Yii::log("CURL error for fine/penalty: $curlError", CLogger::LEVEL_ERROR);
                return null;
            }

            Yii::log("Direct fine/penalty API - HTTP Code: $httpCode, Response length: " . strlen($response), CLogger::LEVEL_INFO);

            if ($httpCode === 200) {
                $data = json_decode($response, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // Convert to objects for consistency
                    $objects = array();
                    foreach ($data as $item) {
                        $objects[] = (object) $item;
                    }
                    Yii::log('Successfully retrieved ' . count($objects) . ' fine/penalty items via direct API', CLogger::LEVEL_INFO);
                    return $objects;
                }
            } else {
                Yii::log("Direct fine/penalty API failed with HTTP $httpCode: " . substr($response, 0, 200), CLogger::LEVEL_WARNING);
            }

            return null;
        } catch (Exception $e) {
            Yii::log('Direct fine/penalty API failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Make enhanced API call with detailed logging and error handling
     */
    private function makeEnhancedApiCall($url, $dataType) {
        try {
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));
            curl_setopt($curl, CURLOPT_TIMEOUT, 15); // 15 second timeout for better reliability
            curl_setopt($curl, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); // For development environments

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $curlError = curl_error($curl);
            curl_close($curl);

            Yii::log("API call for $dataType - HTTP Code: $httpCode, Response length: " . strlen($response), CLogger::LEVEL_INFO);

            if ($curlError) {
                Yii::log("CURL error for $dataType: $curlError", CLogger::LEVEL_ERROR);
                return null;
            }

            if ($httpCode === 200) {
                $data = json_decode($response, true); // Decode as associative array
                if (json_last_error() === JSON_ERROR_NONE && is_array($data)) {
                    // Convert associative arrays back to objects for consistency
                    $objects = array();
                    foreach ($data as $item) {
                        $objects[] = (object) $item;
                    }
                    return $objects;
                } else {
                    Yii::log("JSON decode error for $dataType: " . json_last_error_msg(), CLogger::LEVEL_ERROR);
                    return null;
                }
            } else {
                Yii::log("API call failed for $dataType with HTTP code: $httpCode, Response: " . substr($response, 0, 500), CLogger::LEVEL_WARNING);
                return null;
            }
        } catch (Exception $e) {
            Yii::log("Enhanced API call exception for $dataType: " . $e->getMessage(), CLogger::LEVEL_ERROR);
            return null;
        }
    }

    /**
     * Get robustness data via CRA service for display purposes
     */
    private function getCraRobustnessDataForDisplay() {
        try {
            // Try to use CRA-specific endpoints that might have different permissions
            $url = JService::$baseUrl . "/cra-robustness";
            Yii::log('Attempting CRA robustness service call to: ' . $url, CLogger::LEVEL_INFO);

            $response = $this->makeEnhancedApiCall($url, 'cra-robustness');
            if ($response && is_array($response)) {
                Yii::log('Successfully retrieved ' . count($response) . ' robustness items via CRA service', CLogger::LEVEL_INFO);
                return $response;
            }

            return null;
        } catch (Exception $e) {
            Yii::log('CRA robustness service failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Get fine and penalty data via CRA service for display purposes
     */
    private function getCraFinePenaltyDataForDisplay() {
        try {
            // Try to use CRA-specific endpoints that might have different permissions
            $url = JService::$baseUrl . "/cra-fine-and-penalties";
            Yii::log('Attempting CRA fine/penalty service call to: ' . $url, CLogger::LEVEL_INFO);

            $response = $this->makeEnhancedApiCall($url, 'cra-fine-and-penalties');
            if ($response && is_array($response)) {
                Yii::log('Successfully retrieved ' . count($response) . ' fine/penalty items via CRA service', CLogger::LEVEL_INFO);
                return $response;
            }

            return null;
        } catch (Exception $e) {
            Yii::log('CRA fine/penalty service failed: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
            return null;
        }
    }

    /**
     * Extract robustness data from existing claims (fallback method)
     */
    private function extractRobustnessDataFromClaims() {
        // This method extracts unique robustness entries from existing claims
        // to provide at least the basic data needed for display
        $robustnessData = array();
        $seenIds = array();

        // Get all claims for this CRA to extract robustness data
        $allClaims = $this->getAllClaimsForExtraction();

        foreach ($allClaims as $claim) {
            if (isset($claim->robustnessId) && $claim->robustnessId && !in_array($claim->robustnessId, $seenIds)) {
                $robustnessData[] = (object) array(
                    'id' => $claim->robustnessId,
                    'itemName' => isset($claim->robustnessName) ? $claim->robustnessName : 'Robustness Item ' . $claim->robustnessId,
                    'description' => 'Extracted from claims data'
                );
                $seenIds[] = $claim->robustnessId;
            }
        }

        Yii::log('Extracted ' . count($robustnessData) . ' robustness items from claims data', CLogger::LEVEL_INFO);
        return $robustnessData;
    }

    /**
     * Extract fine and penalty data from existing claims (fallback method)
     */
    private function extractFinePenaltyDataFromClaims() {
        // This method extracts unique fine/penalty entries from existing claims
        // to provide at least the basic data needed for display
        $finePenaltyData = array();
        $seenIds = array();

        // Get all claims for this CRA to extract fine/penalty data
        $allClaims = $this->getAllClaimsForExtraction();

        foreach ($allClaims as $claim) {
            if (isset($claim->fineAndPenaltyId) && $claim->fineAndPenaltyId && !in_array($claim->fineAndPenaltyId, $seenIds)) {
                $finePenaltyData[] = (object) array(
                    'id' => $claim->fineAndPenaltyId,
                    'itemName' => isset($claim->fineAndPenaltyName) ? $claim->fineAndPenaltyName : 'Fine/Penalty Item ' . $claim->fineAndPenaltyId,
                    'descriptionOfActs' => 'Extracted from claims data',
                    'penaltiesSanctionsApplied' => 'Contact SCI team for details',
                    'otherRemedies' => 'Contact SCI team for details',
                    'legalBackground' => 'Contact SCI team for details'
                );
                $seenIds[] = $claim->fineAndPenaltyId;
            }
        }

        Yii::log('Extracted ' . count($finePenaltyData) . ' fine/penalty items from claims data', CLogger::LEVEL_INFO);
        return $finePenaltyData;
    }

    /**
     * Get hardcoded robustness data for testing purposes
     */
    private function getHardcodedRobustnessData() {
        Yii::log('Using hardcoded robustness data for testing', CLogger::LEVEL_INFO);

        return array(
            (object) array(
                'id' => 1309866151,
                'itemName' => 'Risk Assessment Level A',
                'description' => 'High-level risk assessment for marketing claims'
            ),
            (object) array(
                'id' => 1496195725,
                'itemName' => 'Risk Assessment Level B',
                'description' => 'Medium-level risk assessment for marketing claims'
            ),
            (object) array(
                'id' => 1234567890,
                'itemName' => 'Risk Assessment Level C',
                'description' => 'Low-level risk assessment for marketing claims'
            )
        );
    }

    /**
     * Get hardcoded fine and penalty data for testing purposes
     */
    private function getHardcodedFinePenaltyData() {
        Yii::log('Using hardcoded fine and penalty data for testing', CLogger::LEVEL_INFO);

        return array(
            (object) array(
                'id' => 1865237751,
                'itemName' => 'Administrative Warning',
                'descriptionOfActs' => 'Minor violation requiring administrative warning',
                'penaltiesSanctionsApplied' => 'Written warning issued',
                'otherRemedies' => 'Corrective action required',
                'legalBackground' => 'Consumer protection regulations'
            ),
            (object) array(
                'id' => 1804178381,
                'itemName' => 'Financial Penalty',
                'descriptionOfActs' => 'Moderate violation requiring financial penalty',
                'penaltiesSanctionsApplied' => 'Monetary fine imposed',
                'otherRemedies' => 'Process improvement required',
                'legalBackground' => 'Advertising standards regulations'
            ),
            (object) array(
                'id' => 1,
                'itemName' => 'Severe Penalty',
                'descriptionOfActs' => 'Serious violation requiring severe penalty',
                'penaltiesSanctionsApplied' => 'Product recall and fine',
                'otherRemedies' => 'Complete process overhaul',
                'legalBackground' => 'Consumer safety regulations'
            )
        );
    }

    /**
     * Test basic API connectivity for debugging
     */
    private function testBasicApiConnectivity() {
        Yii::log('=== TESTING BASIC API CONNECTIVITY ===', CLogger::LEVEL_INFO);

        $testEndpoints = array(
            '/robustness',
            '/fine-and-penalties',
            '/cra-robustness',
            '/cra-fine-and-penalties'
        );

        foreach ($testEndpoints as $endpoint) {
            try {
                $url = JService::$baseUrl . $endpoint;
                Yii::log('Testing endpoint: ' . $url, CLogger::LEVEL_INFO);

                $curl = curl_init($url);
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                    "Accept: application/json",
                    "Authorization: Bearer " . Yii::app()->session['token']
                ));
                curl_setopt($curl, CURLOPT_TIMEOUT, 10);

                $response = curl_exec($curl);
                $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                $curlError = curl_error($curl);
                curl_close($curl);

                if ($curlError) {
                    Yii::log('CURL Error for ' . $endpoint . ': ' . $curlError, CLogger::LEVEL_ERROR);
                } else {
                    Yii::log('Response for ' . $endpoint . ' - HTTP: ' . $httpCode . ', Length: ' . strlen($response), CLogger::LEVEL_INFO);
                    if ($httpCode === 200) {
                        $data = json_decode($response);
                        if (is_array($data)) {
                            Yii::log('SUCCESS: ' . $endpoint . ' returned ' . count($data) . ' items', CLogger::LEVEL_INFO);
                        } else {
                            Yii::log('WARNING: ' . $endpoint . ' returned non-array data', CLogger::LEVEL_WARNING);
                        }
                    } else {
                        Yii::log('FAILED: ' . $endpoint . ' returned HTTP ' . $httpCode, CLogger::LEVEL_WARNING);
                    }
                }
            } catch (Exception $e) {
                Yii::log('Exception testing ' . $endpoint . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            }
        }

        Yii::log('=== API CONNECTIVITY TEST COMPLETE ===', CLogger::LEVEL_INFO);
    }

    /**
     * Get all claims for data extraction purposes
     */
    private function getAllClaimsForExtraction() {
        try {
            // Try to get claims from multiple CRA requests to build a comprehensive dataset
            $url = JService::$baseUrl . "/cra-claims";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200) {
                $claims = json_decode($response);
                return is_array($claims) ? $claims : array();
            }
        } catch (Exception $e) {
            Yii::log('Failed to get claims for extraction: ' . $e->getMessage(), CLogger::LEVEL_WARNING);
        }

        return array();
    }

    /**
     * Process claims data submission with incremental update logic
     * Optimized to only create/update/delete claims that have actually changed
     */
    private function processClaimsData($postData) {
        if (!isset($postData['claimsData']) || empty($postData['claimsData'])) {
            Yii::log('No claims data provided for processing', CLogger::LEVEL_INFO);
            return true; // No claims to process is not an error
        }

        try {
            $newClaimsData = json_decode($postData['claimsData'], true);
            if (!is_array($newClaimsData)) {
                Yii::log('Invalid claims data format: not an array', CLogger::LEVEL_ERROR);
                error_log('CRA processClaimsData: Invalid claims data - not an array. Raw data: ' . substr($postData['claimsData'], 0, 500));
                return false;
            }

            $craId = isset($postData['id']) ? intval($postData['id']) : null;
            if (!$craId) {
                Yii::log('No CRA ID provided for claims processing', CLogger::LEVEL_ERROR);
                error_log('CRA processClaimsData: No CRA ID provided');
                return false;
            }

            // Debug logging for claims data received (simplified)
            error_log('CRA processClaimsData: Processing ' . count($newClaimsData) . ' claims for CRA ID: ' . $craId);
            // Detailed claim logging commented out to reduce log size
            // foreach ($newClaimsData as $index => $claim) {
            //     error_log('CRA processClaimsData: Claim ' . $index . ' - ID: ' . (isset($claim['id']) ? $claim['id'] : 'NO_ID') .
            //              ', Claims: ' . (isset($claim['claims']) ? substr($claim['claims'], 0, 50) : 'NO_CLAIMS'));
            // }

            Yii::log('Starting incremental claims processing for CRA ID: ' . $craId . ' with ' . count($newClaimsData) . ' claims from frontend', CLogger::LEVEL_INFO);
            $existingClaims = $this->fetchExistingClaims($craId);
            if ($existingClaims === false) {
                Yii::log('Failed to fetch existing claims for CRA ID: ' . $craId, CLogger::LEVEL_ERROR);
                return false;
            }

            $analysisStartTime = microtime(true);
            $operations = $this->analyzeClaimsChanges($existingClaims, $newClaimsData);
            $analysisTime = round((microtime(true) - $analysisStartTime) * 1000, 2);

            error_log('CRA processClaimsData: Analysis complete - Create: ' . count($operations['create']) .
                     ', Update: ' . count($operations['update']) .
                     ', Delete: ' . count($operations['delete']) .
                     ', Unchanged: ' . count($operations['unchanged']));

            Yii::log('Claims analysis complete in ' . $analysisTime . 'ms - Create: ' . count($operations['create']) .
                     ', Update: ' . count($operations['update']) .
                     ', Delete: ' . count($operations['delete']) .
                     ', Unchanged: ' . count($operations['unchanged']), CLogger::LEVEL_INFO);

            $oldMethodOperations = count($existingClaims) + count($newClaimsData); // Delete all + create all
            $newMethodOperations = count($operations['create']) + count($operations['update']) + count($operations['delete']);
            $operationsSaved = $oldMethodOperations - $newMethodOperations;

            if ($operationsSaved > 0) {
                Yii::log('Performance improvement: Saved ' . $operationsSaved . ' database operations (' .
                         round(($operationsSaved / max($oldMethodOperations, 1)) * 100, 1) . '% reduction)', CLogger::LEVEL_INFO);
            }

            $executionStartTime = microtime(true);
            error_log('CRA processClaimsData: Starting execution of operations');
            $results = $this->executeClaimsOperations($operations, $craId);
            $executionTime = round((microtime(true) - $executionStartTime) * 1000, 2);

            $totalOperations = count($operations['create']) + count($operations['update']) + count($operations['delete']);
            $successfulOperations = $results['created'] + $results['updated'] + $results['deleted'];

            error_log('CRA processClaimsData: Execution results - Created: ' . $results['created'] . '/' . count($operations['create']) .
                     ', Updated: ' . $results['updated'] . '/' . count($operations['update']) .
                     ', Deleted: ' . $results['deleted'] . '/' . count($operations['delete']));

            $success = ($successfulOperations === $totalOperations);
            Yii::log('Incremental claims processing completed in ' . $executionTime . 'ms for CRA ID: ' . $craId . ' - ' .
                     'Created: ' . $results['created'] . '/' . count($operations['create']) . ', ' .
                     'Updated: ' . $results['updated'] . '/' . count($operations['update']) . ', ' .
                     'Deleted: ' . $results['deleted'] . '/' . count($operations['delete']) . ', ' .
                     'Unchanged: ' . count($operations['unchanged']),
                     $success ? CLogger::LEVEL_INFO : CLogger::LEVEL_ERROR);

            return $success;

        } catch (Exception $e) {
            Yii::log('Error in incremental claims processing: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Fetch existing claims from database for comparison
     */
    private function fetchExistingClaims($craId) {
        try {
            $url = JService::$baseUrl . "/cra-claims/by-request/" . $craId;
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200) {
                $existingClaims = json_decode($response, true);
                if (is_array($existingClaims)) {
                    Yii::log('Fetched ' . count($existingClaims) . ' existing claims for CRA ID: ' . $craId, CLogger::LEVEL_INFO);
                    return $existingClaims;
                } else {
                    Yii::log('No existing claims found for CRA ID: ' . $craId, CLogger::LEVEL_INFO);
                    return array(); // Return empty array for no claims
                }
            } else {
                Yii::log('Failed to fetch existing claims for CRA ID: ' . $craId . '. HTTP Code: ' . $httpCode, CLogger::LEVEL_ERROR);
                return false;
            }
        } catch (Exception $e) {
            Yii::log('Error fetching existing claims: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Analyze changes between existing and new claims data
     * Returns operations to perform: create, update, delete, unchanged
     */
    private function analyzeClaimsChanges($existingClaims, $newClaimsData) {
        $operations = array(
            'create' => array(),
            'update' => array(),
            'delete' => array(),
            'unchanged' => array()
        );

        // Create lookup maps for efficient comparison
        $existingById = array();
        foreach ($existingClaims as $claim) {
            if (isset($claim['id'])) {
                $existingById[$claim['id']] = $claim;
            }
        }

        $newById = array();
        foreach ($newClaimsData as $claim) {
            if (isset($claim['id']) && !empty($claim['id'])) {
                $newById[$claim['id']] = $claim;
            }
        }

        // Identify claims to create (new claims without existing ID)
        foreach ($newClaimsData as $newClaim) {
            if (!isset($newClaim['id']) || empty($newClaim['id']) || !isset($existingById[$newClaim['id']])) {
                $operations['create'][] = $newClaim;
            }
        }

        // Identify claims to update or mark as unchanged
        foreach ($newById as $id => $newClaim) {
            if (isset($existingById[$id])) {
                if ($this->hasClaimChanged($existingById[$id], $newClaim)) {
                    $operations['update'][] = $newClaim;
                } else {
                    $operations['unchanged'][] = $newClaim;
                }
            }
        }

        // Identify claims to delete (exist in database but not in new data)
        foreach ($existingById as $id => $existingClaim) {
            if (!isset($newById[$id])) {
                $operations['delete'][] = $existingClaim;
            }
        }

        return $operations;
    }

    /**
     * Check if a claim has changed by comparing relevant fields
     */
    private function hasClaimChanged($existingClaim, $newClaim) {
        $fieldsToCompare = array('claims', 'framedRisk', 'criticalRisk', 'fineAndPenaltyId', 'detail', 'robustnessId', 'claimType');

        foreach ($fieldsToCompare as $field) {
            $existingValue = isset($existingClaim[$field]) ? $existingClaim[$field] : '';
            $newValue = isset($newClaim[$field]) ? $newClaim[$field] : '';

            // Handle null values and empty strings consistently
            if ($existingValue === null) $existingValue = '';
            if ($newValue === null) $newValue = '';

            // Convert to string for comparison to handle type differences
            if (strval($existingValue) !== strval($newValue)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Execute the determined operations (create, update, delete)
     */
    private function executeClaimsOperations($operations, $craId) {
        $results = array(
            'created' => 0,
            'updated' => 0,
            'deleted' => 0
        );

        // Execute create operations
        foreach ($operations['create'] as $claimData) {
            if ($this->createClaim($claimData, $craId)) {
                $results['created']++;
            } else {
                Yii::log('Failed to create new claim for CRA ID: ' . $craId, CLogger::LEVEL_ERROR);
            }
        }

        // Execute update operations
        foreach ($operations['update'] as $claimData) {
            if (isset($claimData['id']) && $this->updateClaim($claimData)) {
                $results['updated']++;
            } else {
                $claimId = isset($claimData['id']) ? $claimData['id'] : 'unknown';
                Yii::log('Failed to update claim ID: ' . $claimId . ' for CRA ID: ' . $craId, CLogger::LEVEL_ERROR);
            }
        }

        // Execute delete operations
        foreach ($operations['delete'] as $claimData) {
            if (isset($claimData['id']) && $this->deleteClaim($claimData['id'])) {
                $results['deleted']++;
            } else {
                $claimId = isset($claimData['id']) ? $claimData['id'] : 'unknown';
                Yii::log('Failed to delete claim ID: ' . $claimId . ' for CRA ID: ' . $craId, CLogger::LEVEL_ERROR);
            }
        }

        return $results;
    }





    /**
     * Update an existing claim
     */
    private function updateClaim($claimData) {
        try {
            if (!isset($claimData['id']) || empty($claimData['id'])) {
                Yii::log('Cannot update claim: missing claim ID', CLogger::LEVEL_ERROR);
                return false;
            }

            $claimId = intval($claimData['id']);

            $apiData = array(
                'claimType' => isset($claimData['claimType']) ? $claimData['claimType'] : 'text',
                'claims' => isset($claimData['claims']) ? $claimData['claims'] : '',
                'detail' => isset($claimData['detail']) ? $claimData['detail'] : '',
                'robustnessId' => isset($claimData['robustnessId']) && $claimData['robustnessId'] !== '' ? intval($claimData['robustnessId']) : null,
                'framedRisk' => isset($claimData['framedRisk']) ? $claimData['framedRisk'] : '',
                'criticalRisk' => isset($claimData['criticalRisk']) ? $claimData['criticalRisk'] : '',
                'fineAndPenaltyId' => isset($claimData['fineAndPenaltyId']) && $claimData['fineAndPenaltyId'] !== '' ? intval($claimData['fineAndPenaltyId']) : null
            );

            Yii::log('Updating claim ID: ' . $claimId . ' with data: ' . json_encode($apiData), CLogger::LEVEL_INFO);
            error_log('CRA updateClaim: Updating claim ID: ' . $claimId . ' with data: ' . json_encode($apiData));

            $url = JService::$baseUrl . "/cra-claims/" . $claimId;
            error_log('CRA updateClaim: API URL: ' . $url);

            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($apiData));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            error_log('CRA updateClaim: HTTP Code: ' . $httpCode . ', Response: ' . substr($response, 0, 200));

            if ($httpCode === 200 || $httpCode === 204) {
                error_log('CRA updateClaim: Successfully updated claim ID: ' . $claimId);
                return true;
            } else {
                error_log('CRA updateClaim: Failed to update claim ID: ' . $claimId . '. HTTP Code: ' . $httpCode . ', Response: ' . $response);
                Yii::log('Failed to update claim ID: ' . $claimId . '. HTTP Code: ' . $httpCode . ', Response: ' . $response, CLogger::LEVEL_ERROR);
                return false;
            }

        } catch (Exception $e) {
            Yii::log('Error updating claim: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Delete existing claims for a CRA request (legacy method - kept for backward compatibility)
     */
    private function deleteExistingClaims($craId) {
        try {
            $url = JService::$baseUrl . "/cra-claims/by-request/" . $craId;
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Accept: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200) {
                $existingClaims = json_decode($response, true);
                if (is_array($existingClaims)) {
                    $deleteCount = 0;
                    $totalClaims = count($existingClaims);
                    Yii::log('Found ' . $totalClaims . ' existing claims to delete for CRA ID: ' . $craId, CLogger::LEVEL_INFO);

                    foreach ($existingClaims as $claim) {
                        if (isset($claim['id'])) {
                            if ($this->deleteClaim($claim['id'])) {
                                $deleteCount++;
                            }
                        }
                    }

                    $success = ($deleteCount === $totalClaims);
                    Yii::log('Deleted ' . $deleteCount . '/' . $totalClaims . ' existing claims for CRA ID: ' . $craId,
                             $success ? CLogger::LEVEL_INFO : CLogger::LEVEL_WARNING);
                    return $success;
                } else {
                    Yii::log('No existing claims found for CRA ID: ' . $craId, CLogger::LEVEL_INFO);
                    return true; // No claims to delete is success
                }
            } else {
                Yii::log('Failed to fetch existing claims for CRA ID: ' . $craId . '. HTTP Code: ' . $httpCode, CLogger::LEVEL_ERROR);
                return false;
            }
        } catch (Exception $e) {
            Yii::log('Error deleting existing claims: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Delete a single claim
     */
    private function deleteClaim($claimId) {
        try {
            $url = JService::$baseUrl . "/cra-claims/" . $claimId;
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200 || $httpCode === 204) {
                return true;
            } else {
                Yii::log('Failed to delete claim ' . $claimId . '. HTTP Code: ' . $httpCode . ', Response: ' . $response, CLogger::LEVEL_ERROR);
                return false;
            }
        } catch (Exception $e) {
            Yii::log('Error deleting claim ' . $claimId . ': ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Create a new claim
     */
    private function createClaim($claimData, $craId) {
        try {
            // Validate required claim data
            if (!isset($claimData['claims']) || empty($claimData['claims'])) {
                Yii::log('Cannot create claim: missing claims content', CLogger::LEVEL_ERROR);
                return false;
            }

            $apiData = array(
                'craId' => intval($craId), // Ensure craId is sent as integer
                'claimType' => isset($claimData['claimType']) ? $claimData['claimType'] : 'text',
                'claims' => $claimData['claims'],
                'detail' => isset($claimData['detail']) ? $claimData['detail'] : '',
                'robustnessId' => isset($claimData['robustnessId']) && $claimData['robustnessId'] !== '' ? intval($claimData['robustnessId']) : null,
                'framedRisk' => isset($claimData['framedRisk']) ? $claimData['framedRisk'] : '',
                'criticalRisk' => isset($claimData['criticalRisk']) ? $claimData['criticalRisk'] : '',
                'fineAndPenaltyId' => isset($claimData['fineAndPenaltyId']) && $claimData['fineAndPenaltyId'] !== '' ? intval($claimData['fineAndPenaltyId']) : null
            );

            Yii::log('Creating claim with type: ' . $apiData['claimType'] . ' for CRA ID: ' . $craId, CLogger::LEVEL_INFO);
            Yii::log('API Data: ' . json_encode($apiData), CLogger::LEVEL_INFO);

            $url = JService::$baseUrl . "/cra-claims";
            $curl = curl_init($url);
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($apiData));
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($curl, CURLOPT_HTTPHEADER, array(
                "Content-Type: application/json",
                "Authorization: Bearer " . Yii::app()->session['token']
            ));

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if ($httpCode === 200 || $httpCode === 201) {
                return true;
            } else {
                Yii::log('Failed to create claim. HTTP Code: ' . $httpCode . ', Response: ' . $response, CLogger::LEVEL_ERROR);
                return false;
            }

        } catch (Exception $e) {
            Yii::log('Error creating claim: ' . $e->getMessage(), CLogger::LEVEL_ERROR);
            return false;
        }
    }

    /**
     * Helper method to get redirect URL based on user role
     * Optimized for code reuse
     */
    private function getRedirectUrlByRole($itemId) {
        if(in_array(Yii::app()->user->groupName, array("SCI","SCI Manager","SCI Staff"))){
            return Yii::app()->createUrl('cra/process', array("id" => $itemId));
        } else {
            return Yii::app()->createUrl('cra/update', array("id" => $itemId));
        }
    }

    /**
     * Helper method to validate submission requirements
     * Optimized for consistent validation
     */
    private function validateSubmissionFields($craForm) {
        $required = array(
            'expositionLevel' => 'Exposition Level',
            'expositionDetail' => 'Exposition Detail',
            'advertisementType' => 'Advertisement Type',
            'requestIds' => 'At least one Request ID'
        );

        foreach ($required as $field => $label) {
            if (empty($craForm->$field)) {
                throw new Exception($label . ' is required for submission');
            }
        }

        // Check if content files are provided
        $hasContentFiles = false;

        // Check for new content files being uploaded
        if (!empty($_FILES['newContentFiles']['name'][0])) {
            $hasContentFiles = true;
        }

        // Check for existing content files (in edit mode)
        if (!empty($craForm->contentFiles)) {
            $contentFilesArray = is_string($craForm->contentFiles) ?
                json_decode($craForm->contentFiles, true) : $craForm->contentFiles;
            if (is_array($contentFilesArray) && count($contentFilesArray) > 0) {
                $hasContentFiles = true;
            }
        }

        if (!$hasContentFiles) {
            throw new Exception('At least one Content file is required for submission');
        }

        // Timeline is optional - no validation required
    }

    /**
     * Helper method to format validation errors
     * Optimized for consistent error handling
     */
    private function formatValidationErrors($craForm) {
        $errors = array();
        foreach ($craForm->getErrors() as $field => $fieldErrors) {
            $errors[] = $field . ': ' . implode(', ', $fieldErrors);
        }
        return 'Validation failed: ' . implode('; ', $errors);
    }

    /**
     * RESTful PHP proxy endpoint for CRA claims (maintains architectural consistency)
     * Handles PUT requests to update claims via backend API
     */
    public function actionCraClaims($id) {
        try {
            // Validate claim ID
            $claimId = intval($id);
            if (!$claimId || $claimId <= 0) {
                throw new Exception('Invalid claim ID');
            }

            // Get HTTP method
            $method = $_SERVER['REQUEST_METHOD'];

            if ($method === 'PUT') {
                // Handle PUT request for claim updates
                $this->handleClaimUpdate($claimId);
            } else {
                throw new Exception('HTTP method not supported: ' . $method);
            }

        } catch (Exception $e) {
            error_log('CRA actionCraClaims: Error - ' . $e->getMessage());
            http_response_code(500);
            header('Content-Type: application/json');
            echo json_encode(array('success' => false, 'error' => $e->getMessage()));
        }
    }

    /**
     * Handle PUT request to update a claim via backend API
     */
    private function handleClaimUpdate($claimId) {
        // Get JSON input from request body
        $jsonInput = file_get_contents('php://input');
        if (empty($jsonInput)) {
            throw new Exception('No JSON data provided');
        }

        $claimData = json_decode($jsonInput, true);
        if (!$claimData) {
            throw new Exception('Invalid JSON data format');
        }

        error_log('CRA handleClaimUpdate: Updating claim ID: ' . $claimId);
        error_log('CRA handleClaimUpdate: Claim data: ' . json_encode($claimData));

        // Forward to backend API using existing JService pattern
        $url = JService::$baseUrl . "/cra-claims/" . $claimId;
        $curl = curl_init($url);

        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($claimData));
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HTTPHEADER, array(
            "Content-Type: application/json",
            "Authorization: Bearer " . Yii::app()->session['token']
        ));

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curl);
        curl_close($curl);

        if ($curlError) {
            throw new Exception('Backend API connection error: ' . $curlError);
        }

        if ($httpCode >= 200 && $httpCode < 300) {
            error_log('CRA handleClaimUpdate: Successfully updated claim ID: ' . $claimId);
            header('Content-Type: application/json');

            // Return backend response or success message
            if (!empty($response)) {
                echo $response;
            } else {
                echo json_encode(array('success' => true, 'message' => 'Claim updated successfully'));
            }
        } else {
            error_log('CRA handleClaimUpdate: Backend API error. HTTP Code: ' . $httpCode . ', Response: ' . $response);
            throw new Exception('Backend API error (HTTP ' . $httpCode . '): ' . $response);
        }
    }

public function actionCreateFilter()
	{
		if ($_POST['query'] != "" && NotificationService::count(array(), $_POST['query']) >= 0) {
			$filter = FilterService::create(array(
				"scope" => 'USER',
				"name" => $_POST['name'],
				"query" => $_POST['query'],
				"userId" => Yii::app()->user->id
			));

			$filters = UserService::getFilters(Yii::app()->user->id);

			if (isset($filter) && $filter->id != "") {
				echo json_encode(
					array(
						'success' => true,
						'filters' => $this->renderPartial('_select_filters', array('filters' => $filters), true, true)
					)
				);
			}
		} else {
			echo json_encode(
				array(
					'success' => false
				)
			);
		}
	}

}
